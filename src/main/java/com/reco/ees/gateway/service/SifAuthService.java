package com.reco.ees.gateway.service;

import com.reco.ees.gateway.config.HibernateCacheConfig;
import com.reco.ees.gateway.enums.LogScopeEnum;
import com.reco.ees.gateway.enums.ParametersEnum;
import com.reco.ees.gateway.enums.SifAuthStatusEnum;
import com.reco.ees.gateway.generated.sif.services.GetLoginAuthenticationClientResponse;
import com.reco.ees.gateway.generated.sif.services.SIFIdentityClient;
import com.reco.ees.gateway.generated.sif.services.SecurityType;
import com.reco.ees.gateway.repository.KioskRepository;
import com.reco.ees.gateway.repository.SifAuthRepository;
import com.reco.ees.gateway.repository.model.Kiosk;
import com.reco.ees.gateway.repository.model.SifAuth;
import com.reco.ees.gateway.soap_clients.SoapClientSifAuth;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class SifAuthService {
    private final boolean aggressiveCacheEviction = true; //TODO utile a True solo se, in cache-write-lock, hai problemi di stale data per race-condition in piccola time-window incontrollabile e/o put-from-load failed by hibernate-jcache-caffeine, quindi passi a SifAuth con CacheConcurrencyStrategy.NONSTRICT_READ_WRITE (rilassa lock check ma "aggredisci aggiornamento"). Altrimenti, cioè se risolvi problema sporadico di SifAuth, allora puoi provare a impostare questo a false e riportare SifAuth a CacheConcurrencyStrategy.READ_WRITE
    @Value("${application.name}")
    private String applicationName;
    private final HibernateCacheConfig hibernateCacheConfig;
    private final KioskRepository kioskRepository;
    private final SifAuthRepository sifAuthRepository;
    private final LogService logService;
    private final SoapClientSifAuth soapClientSifAuth;
    private final ParameterService parameterService;

    @Transactional
    public boolean performLoginForKiosks(List<Kiosk> involvedKiosks, boolean isEgate) throws InterruptedException {
        int seconds = Integer.parseInt(parameterService.getParameterById(ParametersEnum.SIF_UNTRACEABLE_FREQUENCY_SECONDS.getParametersEnumValue()).getValue());
        int maxRetries = Integer.parseInt(parameterService.getParameterById(ParametersEnum.SIF_UNTRACEABLE_MAX_RETRIES.getParametersEnumValue()).getValue());
        int retries = 0;

        GetLoginAuthenticationClientResponse response;
        SIFIdentityClient responseResult;

        List<Kiosk> kiosks = new ArrayList<>(involvedKiosks);
        kiosks.removeIf(kiosk -> kiosk.getIdMachine() == null || kiosk.getIdMachine().isBlank());
        if(kiosks.isEmpty()) {
            log.warn("performLoginForKiosks: No kiosks found (after filtering)");
            return false;
        }

        do {
            String transaction = "Sif auth service";
            String logId = logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction,
                    "Requesting login for: ".concat(kiosks.stream().map(Kiosk::getIdMachine).collect(Collectors.joining(","))), null);
            response = soapClientSifAuth.getLoginAuthenticationClientResponse(kiosks, isEgate);
            responseResult = null;
            if(response != null) {
                responseResult = response.getGetLoginAuthenticationClientResult();
                if(responseResult.isIsAuthenticated()) {
                    logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction,
                            "Kiosks have been successfully authenticated", responseResult.getStatus() != null ? responseResult.getStatus().value() : null, logId);
                    log.info("{} kiosks have been successfully authenticated", kiosks.size());
                    break;
                }
                logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, "Error performing login due to " + responseResult.getMessage(), null);
                log.info("Error during kiosks authentication: {}", responseResult.getMessage());
            }
            log.info("I'll try to perform authentication again in {} seconds", seconds);
            Thread.sleep(seconds * 1000L);

            retries++;
        } while (retries < maxRetries);

        if(retries >= maxRetries) return false;

        List<SecurityType> tokens = responseResult.getSecurity();
        List<SifAuth> sifAuthsToSave = new ArrayList<>();
        boolean hasErrors = false;
        for (SecurityType token : tokens) {
            log.info("Received Token: {}", token.getSecurityToken());
            Kiosk kiosk = kiosks.stream().filter(ksk -> ksk.getIdMachine().equals(token.getIdMachine())).findFirst().orElse(null);
            if(kiosk != null) {
                SifAuth sifAuth = sifAuthRepository.findByKiosk(kiosk);
                if(sifAuth == null) {
                    sifAuth = new SifAuth();
                    sifAuth.setKiosk(kiosk);
                }
                sifAuth.setToken(token.getSecurityToken());
                if(responseResult.getDateOfExpire() != null) sifAuth.setDateOfExpire(responseResult.getDateOfExpire().toGregorianCalendar().getTime()); else hasErrors = true;
                if(responseResult.getLastLoginDateTime() != null) sifAuth.setLastLoginDateTime(responseResult.getLastLoginDateTime().toGregorianCalendar().getTime()); else hasErrors = true;
                sifAuth.setMaxInvalidPasswordAttempts(responseResult.getMaxInvalidPasswordAttempts());
                sifAuth.setMessage(responseResult.getMessage());
                if(responseResult.getPasswordExpirationDate() != null) sifAuth.setPasswordExpirationDate(responseResult.getPasswordExpirationDate().toGregorianCalendar().getTime()); else hasErrors = true;
                if(responseResult.getStatus() != null) sifAuth.setStatus(responseResult.getStatus().value()); else hasErrors = true;
                sifAuth.setUsername(responseResult.getUserName());
                // sifAuth.setAppstatus(); // appstatus viene gestito da updateSifAuthStatus
                sifAuth.setApplication(applicationName);

                sifAuthsToSave.add(sifAuth);
            } else {
                log.warn("SifAuthService: Kiosk not found for idMachine: {}", token.getIdMachine());
                hasErrors = true;
            }
        }

        if (!sifAuthsToSave.isEmpty()) {
            List<SifAuth> savedSifAuths = sifAuthRepository.saveAllAndFlush(sifAuthsToSave);
            if (hibernateCacheConfig != null && aggressiveCacheEviction) {
                log.warn("Aggressively evicting SIF_AUTH_REGION_NAME after SIF Auth operation.");
                hibernateCacheConfig.evictSpecificCacheRegion(HibernateCacheConfig.SIF_AUTH_REGION_NAME);
                // hibernateCacheConfig.evictSpecificCacheRegion(HibernateCacheConfig.SIF_AUTH_QUERIES_REGION_NAME);
            }
            // Al commit di questa transazione, Hibernate invaliderà automaticamente le istanze di SifAuth modificate dalla cache L2.
            // Se questo non è sufficiente a causa di concorrenza, proviamo l'invalidazione esplicita:
            /*log.debug("Attempting explicit L2 cache eviction for {} SifAuth entities after performLoginForKiosks", savedSifAuths.size());
            for (SifAuth savedAuth : savedSifAuths) {
                if (savedAuth.getIdSifAuth() != null) {
                    hibernateCacheConfig.evictSpecificEntityFromCache(SifAuth.class, savedAuth.getIdSifAuth());
                }
            }*/
            //log.warn("Aggressively evicting SIF_AUTH_REGION_NAME after SIF login.");
            //hibernateCacheConfig.evictSpecificCacheRegion(HibernateCacheConfig.SIF_AUTH_REGION_NAME);
            // In alternativa, per un approccio più aggressivo (ma meno performante):
            // hibernateCacheConfig.evictSpecificCacheRegion(HibernateCacheConfig.SIF_AUTH_REGION_NAME);
        }
        updateSifAuthStatus(applicationName, !hasErrors ? SifAuthStatusEnum.AUTH_OK.getSifAuthStatusEnumValue() : SifAuthStatusEnum.AUTH_KO.getSifAuthStatusEnumValue(), involvedKiosks);

        return !hasErrors;
    }

    /*public void facesContention(String application, String appstatus) {
        sifAuthRepository.facesContention(UUID.randomUUID().toString(), application, appstatus);
    }

    public void resetContention(String applicationName) {
        sifAuthRepository.resetContention(applicationName);
    }*/

    @Transactional
    public void updateSifAuthStatus(String applicationName, String status, List<Kiosk> specificKiosks) {
        List<SifAuth> sifAuthsToUpdate = new ArrayList<>();
        
        if (specificKiosks != null && !specificKiosks.isEmpty()) {
            // Evict dalla cache prima di modificare
            for (Kiosk kiosk : specificKiosks) {
                SifAuth sifAuth = sifAuthRepository.findByKiosk(kiosk);
                if (sifAuth != null) {
                    //hibernateCacheConfig.evictSpecificEntityFromCache(SifAuth.class, sifAuth.getIdSifAuth());
                    sifAuth.setApplication(applicationName);
                    sifAuth.setAppstatus(status);
                    sifAuthsToUpdate.add(sifAuth);
                } else {
                    log.warn("SifAuth not found for Kiosk ID: {} while trying to update status to {}", 
                             kiosk.getIdKiosk(), status);
                }
            }
        } else {
            // Aggiorna tutti i SifAuth per l'applicazione data
            List<SifAuth> allSifAuthsForApp = sifAuthRepository.findAll();
            for (SifAuth sifAuth : allSifAuthsForApp) {
                //hibernateCacheConfig.evictSpecificEntityFromCache(SifAuth.class, sifAuth.getIdSifAuth());
                if (sifAuth.getApplication() == null || sifAuth.getApplication().equals(applicationName)) {
                    sifAuth.setApplication(applicationName);
                    sifAuth.setAppstatus(status);
                    sifAuthsToUpdate.add(sifAuth);
                }
            }
        }

        if (!sifAuthsToUpdate.isEmpty()) {
            sifAuthRepository.saveAllAndFlush(sifAuthsToUpdate);
            // Evict anche la cache delle query
            //hibernateCacheConfig.evictSpecificCacheRegion("sifAuthQueries");
            if (hibernateCacheConfig != null && aggressiveCacheEviction) {
                log.warn("Aggressively evicting SIF_AUTH_REGION_NAME after SIF Auth operation.");
                hibernateCacheConfig.evictSpecificCacheRegion(HibernateCacheConfig.SIF_AUTH_REGION_NAME);
                // La riga seguente DEVE ESSERE RIMOSSA O COMMENTATA se ancora presente:
                // log.info("Attempting to manually evict cache region: sifAuthQueries");
                // hibernateCacheConfig.evictSpecificCacheRegion(HibernateCacheConfig.SIF_AUTH_QUERIES_REGION_NAME);
            }
            
            log.info("Updated appstatus to {} for {} SifAuth records (application: {}, specificKiosks: {})",
                    status, sifAuthsToUpdate.size(), applicationName, 
                    specificKiosks != null && !specificKiosks.isEmpty());
        } else {
            log.info("No SifAuth records found to update appstatus to {} (application: {}, specificKiosks: {})",
                    status, applicationName, specificKiosks != null && !specificKiosks.isEmpty());
        }
    }

    public boolean shouldDoSifAuth(Kiosk... kiosks) {
        int refreshFrequencyHours = Integer.parseInt(parameterService.getParameterById(ParametersEnum.SIF_AUTHENTICATION_REFRESH_FREQUENCY_HOURS.getParametersEnumValue()).getValue());
        Date thresholdDateTime = Date.from(LocalDateTime.now().minusHours(refreshFrequencyHours).atZone(ZoneId.systemDefault()).toInstant());

        if (kiosks != null && kiosks.length > 0) {
            long result = sifAuthRepository.countByKioskInAndLastLoginDateTimeBeforeOrLastLoginDateTimeIsNull(Arrays.asList(kiosks), thresholdDateTime);
            long kiosksWithoutSifAuth = kioskRepository.countByIdKioskInAndSifAuthIsNull(Arrays.stream(kiosks).map(Kiosk::getIdKiosk).collect(Collectors.toList()));
            result += kiosksWithoutSifAuth;
            return result > 0;
        }

        long outdatedSifAuthCount = sifAuthRepository.countByLastLoginDateTimeBeforeOrLastLoginDateTimeIsNull(thresholdDateTime);
        //    long countByLastLoginDateTimeBeforeOrLastLoginDateTimeIsNullAndAppStatus(LocalDateTime thresholdDateTime, SifAuthStatusEnum appStatus);
        long totalSifAuthCount = sifAuthRepository.count();
        long totalKioskCount = kioskRepository.count();

        return outdatedSifAuthCount > 0 || totalSifAuthCount == 0 || totalSifAuthCount != totalKioskCount;
    }

    public Date getSifPasswordExpirationDate() {
        return sifAuthRepository.getSifPasswordExpirationDate();
    }

    public List<Object[]> getAllSifPasswordsExpirationDates() {
        return sifAuthRepository.getAllSifPasswordsExpirationDates();
    }

    /*@Transactional
    public void updateSifAuthApplication(String oldApplication, String newApplication) {
        sifAuthRepository.updateSifAuthApplication(oldApplication, newApplication);
    }*/
}