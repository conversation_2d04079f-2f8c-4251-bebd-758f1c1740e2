package com.reco.ees.gateway.soap_clients;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.reco.ees.gateway.enums.*;
import com.reco.ees.gateway.repository.AutoScheduledJobsLockRepository;
import com.reco.ees.gateway.repository.KioskRepository;
import com.reco.ees.gateway.repository.SifAuthRepository;
import com.reco.ees.gateway.repository.SifParametersRepository;
import com.reco.ees.gateway.repository.model.AutoScheduledJobsLock;
import com.reco.ees.gateway.repository.model.Kiosk;
import com.reco.ees.gateway.service.*;
import com.reco.ees.gateway.util.SoapUtils;
import com.reco.ees.gateway.util.UtilsService;
import eu.europa.schengen.ees.xsd.v1.SearchByPersonalDataRequestMessageType;
import eu.europa.schengen.ees.xsd.v1.SearchByPersonalDataResponseMessageType;
import eu.europa.schengen.ees_ns.xsd.v1.*;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.soap.MessageFactory;
import jakarta.xml.soap.SOAPConstants;
import jakarta.xml.soap.SOAPException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Component;
import org.springframework.ws.client.core.support.WebServiceGatewaySupport;
import org.springframework.ws.client.support.interceptor.ClientInterceptor;
import org.springframework.ws.soap.saaj.SaajSoapMessageFactory;
import org.springframework.ws.transport.http.HttpsUrlConnectionMessageSender;

import javax.xml.namespace.QName;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.UnrecoverableKeyException;
import java.security.cert.CertificateException;
import java.time.LocalDateTime;
import java.util.List;

@Slf4j
@Component
public class SoapClientWfe extends WebServiceGatewaySupport {
    private final LogService logService;
    final SoapInterceptorWfe soapInterceptorWfe;
    private final SoapUtils soapUtils;
    private final SoapClientFake soapClientFake;
    private final ParameterService parameterService;
    private final SifParametersRepository sifParametersRepository;
    private final KioskRepository kioskRepository;
    private final SifAuthService sifAuthService;
    private final AutoScheduledJobsLockRepository autoScheduledJobsLockRepository;
    private final DynamicSchedulingService dynamicSchedulingService;
    private final DossierService dossierService;
    private final SifAuthRepository sifAuthRepository;
    private final UtilsService utilsService;

    @Value("${generated.sif.async.classes:com.reco.ees.gateway.generated.sif.response}")
    private String sifAsyncPackage;
    @Value("${generated.sif.wfe.soap.version:1.2}")
    private String eesNsSoapVersion;
    @Value("${generated.sif.wfe.classes:eu.europa.schengen.ees_ns.xsd.v1}")
    private String eesNsPackage;
    @Value("${fake.clients.returns:false}")
    private Boolean fakeClientsReturns;

    public SoapClientWfe(LogService logService, SoapInterceptorWfe soapInterceptorWfe, SoapUtils soapUtils, SoapClientFake soapClientFake, ParameterService parameterService, SifParametersRepository sifParametersRepository, KioskRepository kioskRepository, SifAuthService sifAuthService, AutoScheduledJobsLockRepository autoScheduledJobsLockRepository, DynamicSchedulingService dynamicSchedulingService, DossierService dossierService, SifAuthRepository sifAuthRepository, UtilsService utilsService) {
        this.logService = logService;
        this.soapInterceptorWfe = soapInterceptorWfe;
        this.soapUtils = soapUtils;
        this.soapClientFake = soapClientFake;
        this.parameterService = parameterService;
        this.sifParametersRepository = sifParametersRepository;
        this.kioskRepository = kioskRepository;
        this.sifAuthService = sifAuthService;
        this.autoScheduledJobsLockRepository = autoScheduledJobsLockRepository;
        this.dynamicSchedulingService = dynamicSchedulingService;
        this.dossierService = dossierService;
        this.sifAuthRepository = sifAuthRepository;
        this.utilsService = utilsService;
    }

    public SoapClientWfe createSoapClientWfe(Object header, boolean isSurveyRequest) {
        this.setDefaultUri(parameterService.getParameterById(isSurveyRequest ?
                ParametersEnum.SIF_SURVEY_URI.getParametersEnumValue() :
                ParametersEnum.SIF_WFE_URI.getParametersEnumValue()).getValue());
        Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
        jaxb2Marshaller.setPackagesToScan(sifAsyncPackage, eesNsPackage);
        try {
            jaxb2Marshaller.afterPropertiesSet();
        } catch (Exception e) {
            log.error("Error during marshaller creation of the WFE Soap Client: {}", e.getMessage());
        }
        this.setMarshaller(jaxb2Marshaller);
        this.setUnmarshaller(jaxb2Marshaller);

        HttpsUrlConnectionMessageSender httpsSender = null;
        try {
            httpsSender = soapUtils.prepareSecureChannel();
            log.info("HTTPS ready for the WFE Soap Client " + utilsService.getCurrentAndCallerMethodInfo());
        } catch (KeyStoreException | IOException | CertificateException | NoSuchAlgorithmException |
                 UnrecoverableKeyException | KeyManagementException e) {
            log.error("Error during secure channel preparation of the WFE Soap Client " + utilsService.getCurrentAndCallerMethodInfo());
            throw new RuntimeException(e);
        }
        this.setMessageSender(httpsSender);
        soapInterceptorWfe.setRequestHeader(header);
        if(eesNsSoapVersion.equals("1.1")) {
            try {
                this.getWebServiceTemplate().setMessageFactory(new SaajSoapMessageFactory(MessageFactory.newInstance(SOAPConstants.SOAP_1_1_PROTOCOL)));
            } catch (SOAPException e) {
                throw new RuntimeException(e);
            }
        } else if(eesNsSoapVersion.equals("1.2")) {
            try {
                this.getWebServiceTemplate().setMessageFactory(new SaajSoapMessageFactory(MessageFactory.newInstance(SOAPConstants.SOAP_1_2_PROTOCOL)));
            } catch (SOAPException e) {
                throw new RuntimeException(e);
            }
        }
        this.getWebServiceTemplate().setInterceptors(new ClientInterceptor[]{soapInterceptorWfe});

        return this;
    }

    public boolean performStartBorderControl(StartBorderControlRequestMessageType request, HeaderAsyncRequestType headerAsyncRequestType, String idDossier) throws InterruptedException {
        QName qName = new QName(sifParametersRepository.findByType(SifParametersEnum.WFE_NAMESPACE_URI.toString()), "StartBorderControlRequest");
        JAXBElement<StartBorderControlRequestMessageType> jaxbElement = new JAXBElement<>(qName, StartBorderControlRequestMessageType.class, request);
        return contactSifService(headerAsyncRequestType, jaxbElement, DossierOperationEnum.START_BORDER_CONTROL, idDossier);
    }

    public boolean performAddDataToBorderControl(AddDataToBorderControlRequestMessageType request, HeaderAsyncRequestType headerAsyncRequestType, String idDossier) throws InterruptedException, JsonProcessingException {
        QName qName = new QName(sifParametersRepository.findByType(SifParametersEnum.WFE_NAMESPACE_URI.toString()), "AddDataToBorderControlRequest");
        JAXBElement<AddDataToBorderControlRequestMessageType> jaxbElement = new JAXBElement<>(qName, AddDataToBorderControlRequestMessageType.class, request);
        return contactSifService(headerAsyncRequestType, jaxbElement, DossierOperationEnum.ADD_DATA_TO_BORDER_CONTROL, idDossier);
    }

    public boolean performAbortBorderControl(AbortBorderControlRequestMessageType request, HeaderRequestType headerRequestType, String idDossier) {
        return getAbortBorderControlResponse(request, headerRequestType, idDossier);
    }

    public EndBorderControlResponseMessageType performEndBorderControl(EndBorderControlRequestMessageType request, HeaderRequestType headerRequestType) {
        return getEndBorderControlResponse(request, headerRequestType);
    }

    public SurveyGetResponseType performGetSurvey(SurveyGetRequestType request, HeaderRequestType headerRequestType) {
        return getGetSurveyResponse(request, headerRequestType);
    }

    @SuppressWarnings("unchecked")
    private SurveyGetResponseType getGetSurveyResponse(SurveyGetRequestType request, HeaderRequestType headerRequestType) {
        if(fakeClientsReturns) return soapClientFake.fakeSurveyGetResponseType();

        SoapClientWfe soapClientWfe = createSoapClientWfe(headerRequestType, true);
        QName qName = new QName(sifParametersRepository.findByType(SifParametersEnum.WFE_NAMESPACE_URI.toString()), "SurveyGetRequest");
        JAXBElement<SurveyGetRequestType> jaxbElement = new JAXBElement<>(qName, SurveyGetRequestType.class, request);

        String xmlRequest = SoapUtils.marshalToXml(jaxbElement, SurveyGetRequestType.class, SurveyGetResponseType.class);

        String transaction = DossierOperationEnum.GET_SURVEY.getDossierOperationEnumValue() + " performed by the client " + headerRequestType.getSIFHeader().getIdChiamante();
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);

        JAXBElement<SurveyGetResponseType> response = null;
        try {
            response = (JAXBElement<SurveyGetResponseType>) soapClientWfe.getWebServiceTemplate().marshalSendAndReceive(jaxbElement);
        } catch (Exception e) {
            log.error("Error during {} marshalSendAndReceive for SIF Transaction with Id: {} due to {}", DossierOperationEnum.GET_SURVEY, headerRequestType.getTransactionID(), e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction,
                    String.format("Error during getGetSurveyResponse marshalSendAndReceive for SIF Transaction with Id: %s due to %s", headerRequestType.getTransactionID(), e.getMessage()),
                    null, logId);
            return null;
        }
        if(response != null) {
            String responseXml = SoapUtils.marshalToXml(response, SurveyGetRequestType.class, SurveyGetResponseType.class);
            if(response.getValue() != null && response.getValue().getReturnCodes() != null && response.getValue().getReturnCodes().getErrorCodes() != null && response.getValue().getReturnCodes().getErrorCodes().getErrorCode() != null && !response.getValue().getReturnCodes().getErrorCodes().getErrorCode().isEmpty())
                logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), String.join(", ", response.getValue().getReturnCodes().getErrorCodes().getErrorCode()), logId);
             else logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), null, logId);
        }

        return response != null ? response.getValue() : null;
    }

    @SuppressWarnings("unchecked")
    private EndBorderControlResponseMessageType getEndBorderControlResponse(EndBorderControlRequestMessageType request, HeaderRequestType headerRequestType) {
        if(fakeClientsReturns) return soapClientFake.fakeEndBorderControlResponseType();

        SoapClientWfe soapClientWfe = createSoapClientWfe(headerRequestType, false);

        QName qName = new QName(sifParametersRepository.findByType(SifParametersEnum.WFE_NAMESPACE_URI.toString()), "EndBorderControlRequest");
        JAXBElement<EndBorderControlRequestMessageType> jaxbElement = new JAXBElement<>(qName, EndBorderControlRequestMessageType.class, request);

        String xmlRequest = SoapUtils.marshalToXml(jaxbElement, EndBorderControlRequestMessageType.class, EndBorderControlResponseMessageType.class);

        String transaction = DossierOperationEnum.END_BORDER_CONTROL.getDossierOperationEnumValue() + " performed by the client " + headerRequestType.getSIFHeader().getIdChiamante();
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);

        JAXBElement<EndBorderControlResponseMessageType> response = null;
        try {
            response = (JAXBElement<EndBorderControlResponseMessageType>) soapClientWfe.getWebServiceTemplate().marshalSendAndReceive(jaxbElement);
        } catch (Exception e) {
            log.error("Error during {} marshalSendAndReceive: {}", DossierOperationEnum.END_BORDER_CONTROL.getDossierOperationEnumValue(), e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction,
                    String.format("Error during getEndBorderControlResponse marshalSendAndReceive for SIF Transaction with Id: %s due to %s", headerRequestType.getTransactionID(), e.getMessage()),
                    null, logId);
        }
        if(response != null) {
            String responseXml = SoapUtils.marshalToXml(response, EndBorderControlRequestMessageType.class, EndBorderControlResponseMessageType.class);
            if(response.getValue() != null && response.getValue().getReturnCodes() != null && response.getValue().getReturnCodes().getErrorCodes() != null && response.getValue().getReturnCodes().getErrorCodes().getErrorCode() != null && !response.getValue().getReturnCodes().getErrorCodes().getErrorCode().isEmpty())
                logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), String.join(", ", response.getValue().getReturnCodes().getErrorCodes().getErrorCode()), logId);
            else logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), null, logId);
        }

        return response != null ? response.getValue() : null;
    }

    @SuppressWarnings("unchecked")
    private boolean contactSifService(HeaderAsyncRequestType headerAsyncRequestType, JAXBElement<?> jaxbElement, DossierOperationEnum dossierOperationEnum, String idDossier) throws InterruptedException {
        if(fakeClientsReturns) return true;
        // TODO terminati i tentativi di ricontatto sif allora logga ma elimina/non salvare il dossier (in generale quando questo metodo ritorna false)
        int seconds = Integer.parseInt(parameterService.getParameterById(ParametersEnum.SIF_UNTRACEABLE_FREQUENCY_SECONDS.getParametersEnumValue()).getValue());
        int maxRetries = Integer.parseInt(parameterService.getParameterById(ParametersEnum.SIF_UNTRACEABLE_MAX_RETRIES.getParametersEnumValue()).getValue());
        int retries = 0;

        SoapClientWfe soapClientWfe = createSoapClientWfe(headerAsyncRequestType, false);
        JAXBElement<Object> responseAckBody;
        boolean result = true;
        String xmlRequest = null;
        do {
            switch (dossierOperationEnum) {
                case START_BORDER_CONTROL -> xmlRequest = SoapUtils.marshalToXml(jaxbElement, StartBorderControlRequestMessageType.class, StartBorderControlResponseMessageType.class);
                case ADD_DATA_TO_BORDER_CONTROL -> xmlRequest = SoapUtils.marshalToXml(jaxbElement, AddDataToBorderControlRequestMessageType.class, AddDataToBorderControlResponseMessageType.class);
            }
            String transaction = dossierOperationEnum.getDossierOperationEnumValue() + " performed by the client " + headerAsyncRequestType.getSIFHeader().getIdChiamante() + " for dossier " + idDossier;

            String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);
            /*dossierService.findByIdMessage(idDossier).ifPresent(dossier -> {
                if (dossier.getSifInteractionCount() != null) dossier.setSifInteractionCount(dossier.getSifInteractionCount() + 1);
                else dossier.setSifInteractionCount(1);
                dossier.setLastSifInteractionDate(new Date());
                dossierService.save(dossier);
            });*/
            try {
                responseAckBody = (JAXBElement<Object>) soapClientWfe.getWebServiceTemplate().marshalSendAndReceive(jaxbElement);
            } catch (Exception e) {
                log.error("Error during contactSifService {} marshalSendAndReceive: {}", dossierOperationEnum.getDossierOperationEnumValue(), e.getMessage());
                logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction,
                        String.format("Error during contactSifService marshalSendAndReceive for SIF Transaction with Id: %s due to %s", headerAsyncRequestType.getTransactionID(), e.getMessage()),
                        null, logId);
                log.info("I'll try to perform {} again in {} seconds for the client {}",
                        dossierOperationEnum.getDossierOperationEnumValue(),
                        seconds,
                        headerAsyncRequestType.getSIFHeader().getIdChiamante());
                Thread.sleep(seconds * 1000L);
                retries++;
                continue;
            }

            String responseXml = null;
            String response = null;
            switch (dossierOperationEnum) {
                case START_BORDER_CONTROL -> responseXml = SoapUtils.marshalToXml(responseAckBody, StartBorderControlRequestMessageType.class, StartBorderControlResponseMessageType.class);
                case ADD_DATA_TO_BORDER_CONTROL -> responseXml = SoapUtils.marshalToXml(responseAckBody, AddDataToBorderControlRequestMessageType.class, AddDataToBorderControlResponseMessageType.class);
            }
            response = (responseAckBody != null && responseAckBody.getValue() != null) ? (String) responseAckBody.getValue() : null;
            String responseCode = response != null && !response.isBlank() ? response : null;
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), responseCode, logId);
            if(response != null && response.isBlank()) {
                log.info("{} successfully taken over by SIF for the client {}",
                        dossierOperationEnum.getDossierOperationEnumValue(),
                        headerAsyncRequestType.getSIFHeader().getIdChiamante());
                break;
            }

            SifErrorEnum sifError = SifErrorEnum.getKeyFromValue(response);
            if(sifError != null) {
                log.warn("Error {} occurred on {} for client {}",
                        sifError.getValue().toUpperCase(),
                        dossierOperationEnum.getDossierOperationEnumValue(),
                        headerAsyncRequestType.getSIFHeader().getIdChiamante());

                switch (sifError) { //TODO: oltre a questi log scriverei anche su DB in eventLog (questo vale anche per altri log sparsi nell'app probabilmente)
                    case EES_OFFLINE, CONNECTION_TIMEOUT -> { // PROVO A CONTATTARE NUOVAMENTE SIF SECONDO I PARAMETRI maxRetries e seconds
                        log.info("I'll try to perform {} again in {} seconds for the client {}",
                                dossierOperationEnum.getDossierOperationEnumValue(),
                                seconds,
                                headerAsyncRequestType.getSIFHeader().getIdChiamante());
                        Thread.sleep(seconds * 1000L);
                    }
                    case NOT_VALID_TOKEN -> { // FACCIO NUOVAMENTE LOGIN PER KIOSK E CONTATTO NUOVAMENTE SIF
                        Kiosk kiosk = kioskRepository.findByIdMachine(headerAsyncRequestType.getSIFHeader().getIdChiamante());
                        AutoScheduledJobsLock autoScheduledJobsLock = null;
                        autoScheduledJobsLock = autoScheduledJobsLockRepository.findByJobName(AutoScheduledJobsLockEnum.SIF_AUTH_JOB.getAutoScheduledJobsLockEnumValue());
                        int refreshFrequencyHours = Integer.parseInt(parameterService.getParameterById(ParametersEnum.SIF_AUTHENTICATION_REFRESH_FREQUENCY_HOURS.getParametersEnumValue()).getValue());
                        LocalDateTime thresholdTime = LocalDateTime.now().minusHours(refreshFrequencyHours);
                        while (autoScheduledJobsLock != null && autoScheduledJobsLock.getLockedAt().isAfter(thresholdTime)) {
                            log.info("SoapClientWFE: Lock already exists and is not expired for SifAuthJob");
                            Thread.sleep(seconds * 1000L);
                            autoScheduledJobsLock = autoScheduledJobsLockRepository.findByJobName(AutoScheduledJobsLockEnum.SIF_AUTH_JOB.getAutoScheduledJobsLockEnumValue());
                        }
                        if((sifAuthRepository.findByKiosk(kiosk) == null || sifAuthService.shouldDoSifAuth(kiosk)) && !dossierService.isKioskIdPresent(kiosk.getIdKiosk())) {
                            dossierService.addKioskId(kiosk.getIdKiosk());
                            dynamicSchedulingService.runAndWaitSifLoginForSpecificKiosks(List.of(kiosk)); //rifaccio sifLogin casomai l'attesa in modo da assicurarmi login di questo kiosk dopo l'eventuale attesa (appena finita) di rilascio di autoScheduledJobsLock (che magari non comprendeva questo kiosk specifico)
                            dossierService.removeKioskId(kiosk.getIdKiosk());
                            result = !sifAuthService.shouldDoSifAuth(kiosk); //rifaccio shouldDoSifAuth() per vedere se il login ha avuto successo dato che è stato avviato con forceCoreLogicExecution=true
                        }
                        //sifAuthJob.sifLogin(List.of(kiosk)); //rifaccio sifLogin senza forceCoreLogicExecution in modo da verificare se l'eventuale attesa (appena finita) di rilascio di autoScheduledJobsLock abbia considerato anche questo Kiosk specifico (in quanto sifLogin fa shouldDoSifAuth() internamente), quindi ripeto subito dopo shouldDoSifAuth() perchè potrebbero poi esserci stati altri problemi che devono farmi tornare false qui
                        /*result = sifAuthService.performLoginForKiosks(List.of(kiosk)); // SE LOGIN NON VA A BUON FINE FINIREMO POI PER CONTRASSEGNARE IL DOSSIER COME COMPLETATO
                        logService.saveEventLog("SifAuthJob by SoapClientWfe completed " + (result ? "successfully" : "with problems"), null, null);
                        sifAuthService.updateSifAuthStatus(applicationName, result ? SifAuthStatusEnum.AUTH_OK.getSifAuthStatusEnumValue() : SifAuthStatusEnum.AUTH_KO.getSifAuthStatusEnumValue(), List.of(kiosk));*/
                    }
                    default -> {
                        log.warn("Processing for dossier has been definitively concluded due to an intentionally unhandled error {} occurred on {} for client {}. ",
                                responseAckBody != null ? responseAckBody.getValue() : null,
                                dossierOperationEnum.getDossierOperationEnumValue(),
                                headerAsyncRequestType.getSIFHeader().getIdChiamante());
                        return false;
                    }
                }
            } else { // ERRORE SCONOSCIUTO -> CONCLUDO PROCESSING DOSSIER
                log.warn("Processing for dossier has been definitively concluded due to an unknown error {} occurred on {} for client {}. ",
                        responseAckBody != null ? responseAckBody.getValue() : null,
                        dossierOperationEnum.getDossierOperationEnumValue(),
                        headerAsyncRequestType.getSIFHeader().getIdChiamante());
                return false;
            }

            retries++;
        } while (retries < maxRetries);

        return result && retries < maxRetries; // SE retries < maxRetries significa che contatto SIF è andato a buon fine
    }

    @SuppressWarnings("unchecked")
    public SurveyInsertResponseType getSurveyInsertResponse(SurveyInsertRequestType request, HeaderRequestType headerRequestType, String idDossier) {
        SoapClientWfe soapClientWfe = createSoapClientWfe(headerRequestType, true);
        QName qName = new QName(sifParametersRepository.findByType(SifParametersEnum.WFE_NAMESPACE_URI.toString()), "SurveyInsertRequest");
        JAXBElement<SurveyInsertRequestType> jaxbElement = new JAXBElement<>(qName, SurveyInsertRequestType.class, request);

        String transaction = "SurveyInsert performed by the client " + headerRequestType.getSIFHeader().getIdChiamante() + " for dossier " + idDossier;
        String logId = null;

        if(fakeClientsReturns) return soapClientFake.fakeSurveyInsertResponseType();
        //SurveyInsertResponseType response = (SurveyInsertResponseType) soapClientWfe.getWebServiceTemplate().marshalSendAndReceive(jaxbElement);

        String xmlRequest = SoapUtils.marshalToXml(jaxbElement, SurveyInsertRequestType.class, SurveyInsertResponseType.class);
        logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);
        JAXBElement<SurveyInsertResponseType> response;
        try {
            response = (JAXBElement<SurveyInsertResponseType>) soapClientWfe.getWebServiceTemplate().marshalSendAndReceive(jaxbElement);
        } catch (Exception e) {
            log.error("Error during getSurveyInsertResponse marshalSendAndReceive for SIF Transaction with Id: {} due to {}", headerRequestType.getTransactionID(), e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction,
                    String.format("Error during getSurveyInsertResponse marshalSendAndReceive for SIF Transaction with Id: %s due to %s", headerRequestType.getTransactionID(), e.getMessage()),
                    null, logId);
            return null;
        }
        String responseCode = response.getValue() != null && response.getValue().getReturnCodes() != null && response.getValue().getReturnCodes().getErrorCodes() != null ?
                response.getValue().getReturnCodes().getErrorCodes().getErrorCode().toString() : null;
        String responseXml = SoapUtils.marshalToXml(response, SurveyInsertRequestType.class, SurveyInsertResponseType.class);
        logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), responseCode, logId);
        return response.getValue();
    }

    @SuppressWarnings("unchecked")
    public boolean getAbortBorderControlResponse(AbortBorderControlRequestMessageType request, HeaderRequestType headerRequestType, String idDossier) {
        // IMPOSTO MANUALMENTE ENDUSERROLE A 5005 PER SPACCIARMI COME POSTAZIONE MANUALE
        headerRequestType.getUser().setEndUserRole("5005");
        SoapClientWfe soapClientWfe = createSoapClientWfe(headerRequestType, false);
        QName qName = new QName(sifParametersRepository.findByType(SifParametersEnum.WFE_NAMESPACE_URI.toString()), "AbortBorderControlRequest");
        JAXBElement<AbortBorderControlRequestMessageType> jaxbElement = new JAXBElement<>(qName, AbortBorderControlRequestMessageType.class, request);

        String transaction = "AbortBorderControl performed by the client " + headerRequestType.getSIFHeader().getIdChiamante() + " for dossier " + idDossier;
        String logId = null;

        String xmlRequest = SoapUtils.marshalToXml(jaxbElement, AbortBorderControlRequestMessageType.class, AbortBorderControlResponseMessageType.class);
        logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);
        JAXBElement<AbortBorderControlResponseMessageType> response;
        try {
            response = (JAXBElement<AbortBorderControlResponseMessageType>) soapClientWfe.getWebServiceTemplate().marshalSendAndReceive(jaxbElement);
        } catch (Exception e) {
            log.error("Error during getAbortBorderControlResponse marshalSendAndReceive for SIF Transaction with Id: {} due to {}", headerRequestType.getTransactionID(), e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction,
                    String.format("Error during getAbortBorderControlResponse marshalSendAndReceive for SIF Transaction with Id: %s due to %s", headerRequestType.getTransactionID(), e.getMessage()),
                    null, logId);
            return false;
        }
        String errorCodes = response.getValue() != null && response.getValue().getReturnCodes() != null && response.getValue().getReturnCodes().getErrorCodes() != null ?
                response.getValue().getReturnCodes().getErrorCodes().getErrorCode().toString() : null;
        String responseXml = SoapUtils.marshalToXml(response, AbortBorderControlRequestMessageType.class, AbortBorderControlResponseMessageType.class);
        logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), errorCodes, logId);
        return errorCodes == null;
    }

    @SuppressWarnings("unchecked")
    public SearchByPersonalDataResponseMessageType performSearchByPersonalDataInEES(SearchByPersonalDataRequestMessageType searchByPersonalDataRequestMessageType, HeaderRequestType headerRequestType) {
        if(fakeClientsReturns) return soapClientFake.fakeSearchByPersonalDataInEESResponseType();

        SoapClientWfe soapClientWfe = createSoapClientWfe(headerRequestType, false);
        QName qName = new QName(sifParametersRepository.findByType(SifParametersEnum.WFE_NAMESPACE_URI.toString()), "SearchByPersonalDataRequest");
        JAXBElement<SearchByPersonalDataRequestMessageType> jaxbElement = new JAXBElement<>(qName, SearchByPersonalDataRequestMessageType.class, searchByPersonalDataRequestMessageType);

        String xmlRequest = SoapUtils.marshalToXml(jaxbElement, SearchByPersonalDataRequestMessageType.class, SearchByPersonalDataResponseMessageType.class);

        String transaction = "SearchByPersonalData performed by the client " + headerRequestType.getSIFHeader().getIdChiamante();
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);

        JAXBElement<SearchByPersonalDataResponseMessageType> response = null;
        try {
            response = (JAXBElement<SearchByPersonalDataResponseMessageType>) soapClientWfe.getWebServiceTemplate().marshalSendAndReceive(jaxbElement);
        } catch (Exception e) {
            log.error("Error during {} marshalSendAndReceive: {}", DossierOperationEnum.SEARCH_BY_PERSONAL_DATA_IN_EES.getDossierOperationEnumValue(), e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction,
                    String.format("Error during performSearchByPersonalData marshalSendAndReceive for SIF Transaction with Id: %s due to %s", headerRequestType.getTransactionID(), e.getMessage()),
                    null, logId);
            return null;
        }
        if(response != null) {
            String responseXml = SoapUtils.marshalToXml(response, SearchByPersonalDataRequestMessageType.class, SearchByPersonalDataResponseMessageType.class);
            if(response.getValue() != null && response.getValue().getReturnCodes() != null && response.getValue().getReturnCodes().getErrorCodes() != null && response.getValue().getReturnCodes().getErrorCodes().getErrorCode() != null && !response.getValue().getReturnCodes().getErrorCodes().getErrorCode().isEmpty())
                logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), String.join(", ", response.getValue().getReturnCodes().getErrorCodes().getErrorCode()), logId);
            else logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), null, logId);
        } else {
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction,
                    String.format("Error during performSearchByPersonalData marshalSendAndReceive for SIF Transaction with Id: %s due to %s", headerRequestType.getTransactionID(), "Response is null"),
                    null, logId);
        }
        return response != null ? response.getValue() : null;
    }

    @SuppressWarnings("unchecked")
    public SearchByPersonalDataInVISResponseMessageType performSearchByPersonalDataInVIS(SearchByPersonalDataInVISRequestMessageType searchByPersonalDataInVISRequestMessageType, HeaderRequestType headerRequestType) {
        if(fakeClientsReturns) return soapClientFake.fakeSearchByPersonalDataInVISResponseType();

        SoapClientWfe soapClientWfe = createSoapClientWfe(headerRequestType, false);
        QName qName = new QName(sifParametersRepository.findByType(SifParametersEnum.WFE_NAMESPACE_URI.toString()), "SearchByPersonalDataInVISRequest");
        JAXBElement<SearchByPersonalDataInVISRequestMessageType> jaxbElement = new JAXBElement<>(qName, SearchByPersonalDataInVISRequestMessageType.class, searchByPersonalDataInVISRequestMessageType);

        String xmlRequest = SoapUtils.marshalToXml(jaxbElement, SearchByPersonalDataInVISRequestMessageType.class, SearchByPersonalDataInVISResponseMessageType.class);

        String transaction = "SearchByPersonalDataInVIS performed by the client " + headerRequestType.getSIFHeader().getIdChiamante();
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);

        JAXBElement<SearchByPersonalDataInVISResponseMessageType> response = null;
        try {
            response = (JAXBElement<SearchByPersonalDataInVISResponseMessageType>) soapClientWfe.getWebServiceTemplate().marshalSendAndReceive(jaxbElement);
        } catch (Exception e) {
            log.error("Error during {} marshalSendAndReceive: {}", DossierOperationEnum.SEARCH_BY_PERSONAL_DATA_IN_VIS.getDossierOperationEnumValue(), e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction,
                    String.format("Error during performSearchByPersonalDataInVIS marshalSendAndReceive for SIF Transaction with Id: %s due to %s", headerRequestType.getTransactionID(), e.getMessage()),
                    null, logId);
            return null;
        }
        if(response != null) {
            String responseXml = SoapUtils.marshalToXml(response, SearchByPersonalDataInVISRequestMessageType.class, SearchByPersonalDataInVISResponseMessageType.class);
            if(response.getValue() != null && response.getValue().getReturnCodes() != null && response.getValue().getReturnCodes().getErrorCodes() != null && response.getValue().getReturnCodes().getErrorCodes().getErrorCode() != null && !response.getValue().getReturnCodes().getErrorCodes().getErrorCode().isEmpty())
                logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), String.join(", ", response.getValue().getReturnCodes().getErrorCodes().getErrorCode()), logId);
            else logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), null, logId);
        } else {
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction,
                    String.format("Error during performSearchByPersonalDataInVIS marshalSendAndReceive for SIF Transaction with Id: %s due to %s", headerRequestType.getTransactionID(), "Response is null"),
                    null, logId);
        }
        return response != null ? response.getValue() : null;
    }

    @SuppressWarnings("unchecked")
    public IdentificationResultResponseMessageType performIdentificationResult(IdentificationResultRequestMessageType identificationResultRequestMessageType, HeaderRequestType headerRequestType) {
        if(fakeClientsReturns) return soapClientFake.fakeIdentificationResultResponseType();

        SoapClientWfe soapClientWfe = createSoapClientWfe(headerRequestType, false);
        QName qName = new QName(sifParametersRepository.findByType(SifParametersEnum.WFE_NAMESPACE_URI.toString()), "IdentificationResultRequest");
        JAXBElement<IdentificationResultRequestMessageType> jaxbElement = new JAXBElement<>(qName, IdentificationResultRequestMessageType.class, identificationResultRequestMessageType);

        String xmlRequest = SoapUtils.marshalToXml(jaxbElement, IdentificationResultRequestMessageType.class, IdentificationResultResponseMessageType.class);

        String transaction = "IdentificationResult performed by the client " + headerRequestType.getSIFHeader().getIdChiamante();
        String logId = logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, xmlRequest, null);

        JAXBElement<IdentificationResultResponseMessageType> response = null;
        try {
            response = (JAXBElement<IdentificationResultResponseMessageType>) soapClientWfe.getWebServiceTemplate().marshalSendAndReceive(jaxbElement);
        } catch (Exception e) {
            log.error("Error during {} marshalSendAndReceive: {}", DossierOperationEnum.IDENTIFICATION_RESULT.getDossierOperationEnumValue(), e.getMessage());
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction,
                    String.format("Error during performIdentificationResult marshalSendAndReceive for SIF Transaction with Id: %s due to %s", headerRequestType.getTransactionID(), e.getMessage()),
                    null, logId);
            return null;
        }
        if(response != null) {
            String responseXml = SoapUtils.marshalToXml(response, IdentificationResultRequestMessageType.class, IdentificationResultResponseMessageType.class);
            if(response.getValue() != null && response.getValue().getReturnCodes() != null && response.getValue().getReturnCodes().getErrorCodes() != null && response.getValue().getReturnCodes().getErrorCodes().getErrorCode() != null && !response.getValue().getReturnCodes().getErrorCodes().getErrorCode().isEmpty())
                logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), String.join(", ", response.getValue().getReturnCodes().getErrorCodes().getErrorCode()), logId);
            else logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction, soapUtils.cutResponseXml(responseXml), null, logId);
        } else {
            logService.saveEventLog(LogScopeEnum.EXTERNAL.getValue(), transaction,
                    String.format("Error during performIdentificationResult marshalSendAndReceive for SIF Transaction with Id: %s due to %s", headerRequestType.getTransactionID(), "Response is null"),
                    null, logId);
        }
        return response != null ? response.getValue() : null;
    }
}

