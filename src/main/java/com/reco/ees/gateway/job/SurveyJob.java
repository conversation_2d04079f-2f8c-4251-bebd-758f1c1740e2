package com.reco.ees.gateway.job;

import com.reco.ees.gateway.ShutdownManager;
import com.reco.ees.gateway.enums.DossierStatusEnum;
import com.reco.ees.gateway.enums.LogScopeEnum;
import com.reco.ees.gateway.enums.ParametersEnum;
import com.reco.ees.gateway.kafka.producer.KafkaProducer;
import com.reco.ees.gateway.repository.model.Dossier;
import com.reco.ees.gateway.service.DossierService;
import com.reco.ees.gateway.service.LogService;
import com.reco.ees.gateway.service.ParameterService;
import com.reco.ees.gateway.soap_clients.SoapClientWfe;
import com.reco.ees.gateway.util.DateUtil;
import com.reco.ees.gateway.util.UtilsService;
import eu.europa.schengen.ees_ns.xsd.v1.HeaderRequestType;
import eu.europa.schengen.ees_ns.xsd.v1.SurveyInsertRequestType;
import eu.europa.schengen.ees_ns.xsd.v1.SurveyInsertResponseType;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ListIterator;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class SurveyJob implements Runnable {
    private final KafkaProducer kafkaProducer;
    String checkFrequencySeconds;
    int maxRetries;
    @Value("${fake.clients.returns:false}")
    private String fakeClientsReturns;
    @Value("${to.be.processed.record.limit:16}")
    private int recordLimit;
    @Value("${application.name}")
    private String applicationName;

    private final SoapClientWfe soapClientWfe;
    private final LogService logService;
    private final DossierService dossierService;
    private final UtilsService utilsService;
    private final ParameterService parameterService;
    /*@Autowired
    EntityManagerFactory entityManagerFactory;*/
    /*@Autowired
    private SessionFactory sessionFactory;*/

    ExecutorService executorService = Executors.newVirtualThreadPerTaskExecutor();

    public SurveyJob(SoapClientWfe soapClientWfe, LogService logService, DossierService dossierService, UtilsService utilsService, ParameterService parameterService, KafkaProducer kafkaProducer) {
        this.soapClientWfe = soapClientWfe;
        this.logService = logService;
        this.dossierService = dossierService;
        this.utilsService = utilsService;
        this.parameterService = parameterService;
        this.kafkaProducer = kafkaProducer;
    }

    /*@PostConstruct
    public void init() {
        processSurveys();
    }*/

    @Override
    public void run() {
        log.info("Survey Job started");

        while (ShutdownManager.getIsAppRunning()) {
            ShutdownManager.incrementActiveJobs(ShutdownManager.jobNames.SurveyJob.name());

            checkFrequencySeconds = parameterService.getParameterById(ParametersEnum.DOSSIER_TRANSACTION_CHECK_FREQUENCY_SECONDS.getParametersEnumValue()).getValue();
            maxRetries = Integer.parseInt(parameterService.getParameterById(ParametersEnum.DOSSIER_TRANSACTION_CHECK_MAX_RETRIES.getParametersEnumValue()).getValue());
            List<String> dossierStatusEnumValues = List.of(DossierStatusEnum.EES_NEEDS_SURVEY.getDossierStatusEnumValue(), DossierStatusEnum.SURVEY_SENT_KO.getDossierStatusEnumValue()); //per come è attualmente gestito più avanti, lo stato SURVEY_SENT_KO è utile forse solo in caso di crash del metodo sendSurvey che è appositamente non transazionale
            List<Dossier> dossiers = dossierService.findDossiersByApplicationNameAndStatus(applicationName, dossierStatusEnumValues, recordLimit);
            dossiers.removeIf(dossier ->
                    dossier.getStatus().equalsIgnoreCase(DossierStatusEnum.SURVEY_SENT_KO.getDossierStatusEnumValue()) &&
                            (
                                    (dossier.getSifInteractionCount() != null && dossier.getSifInteractionCount() >= maxRetries) ||
                                            (dossier.getLastSifInteractionDate() != null && dossier.getLastSifInteractionDate().after(DateUtil.addDeltaToDate(new Date(), 0, 0, 0, -Integer.parseInt(checkFrequencySeconds), 0)))
                            )
            );
            //Hibernate.initialize(dossiers.get(0).getSurvey());
            if(dossiers.isEmpty()) {
                utilsService.allDoneAndSleep(ShutdownManager.jobNames.SurveyJob.name());
                continue;
            }

            List<Future<?>> futures = new ArrayList<>();
            for (Dossier dossier : dossiers) {
                /*EntityManager entityManager = entityManagerFactory.createEntityManager();
                EntityTransaction transaction = entityManager.getTransaction();
                transaction.begin();
                dossier = entityManager.find(Dossier.class, dossier.getIdMessage());*/
                futures.add(executorService.submit(() -> surveyThread(dossier)));
            }

            ListIterator<Future<?>> iterator = futures.listIterator(futures.size());
            while (iterator.hasPrevious()) {
                Future<?> future = iterator.previous();
                try {
                    future.get();
                } catch (Exception ex) {
                    log.error("Error processing dossier", ex);
                }
            }

            utilsService.allDoneAndSleep(ShutdownManager.jobNames.SurveyJob.name());
        }
    }

    private void surveyThread(Dossier dossier) {
        log.info("Processing survey for dossier with Id: {}", dossier.getIdMessage());
        /*try (Session session = sessionFactory.openSession()) {
            session.beginTransaction();
            sendSurvey(dossier);
            session.getTransaction().commit();
        } catch (Exception ex) {
            log.error("Error sending survey for dossier with Id: {} due to {}", dossier.getIdMessage(), ex.getMessage());
            dossierService.updateDossierStatus(dossier.getIdMessage(), DossierStatusEnum.SURVEY_SENT_KO.getDossierStatusEnumValue());
        }*/
        /*EntityManager entityManager = entityManagerFactory.createEntityManager();
        EntityTransaction transaction = entityManager.getTransaction();*/
        try {
            //sendSurvey(Objects.requireNonNull(dossier));
            /*transaction.begin();
            dossier = entityManager.find(Dossier.class, dossier.getIdMessage());*/
            Dossier editedDossier = sendSurvey(dossier);
            if(editedDossier.getSifInteractionCount() >= maxRetries || editedDossier.getStatus().equalsIgnoreCase(DossierStatusEnum.SURVEY_SENT_OK.getDossierStatusEnumValue()))
                editedDossier = dossierService.deleteSensitiveData(editedDossier);
            if(editedDossier.getEgateHandled().equals(Boolean.TRUE) && editedDossier.getSifInteractionCount() >= maxRetries) kafkaProducer.sendAbcEgateProblemMessage(editedDossier);
            dossierService.save(editedDossier);
            log.info("Survey for dossier with Id: {} processed {}", dossier.getIdMessage(), dossier.getStatus().equalsIgnoreCase(DossierStatusEnum.SURVEY_SENT_OK.getDossierStatusEnumValue()) ? "successfully" : "unsuccessfully");
            //transaction.commit();
        } catch (Exception ex) {
            log.error("Error sending survey for dossier with Id: {} due to {}", dossier.getIdMessage(), ex.getMessage());
            /*if (transaction.isActive()) {
                transaction.rollback();
            }*/
            dossierService.updateDossierStatus(dossier.getIdMessage(), DossierStatusEnum.SURVEY_SENT_KO.getDossierStatusEnumValue());
        }/* finally {
            entityManager.close();
        }*/
    }

    /*@Modifying
    @Transactional*/
    protected Dossier sendSurvey(Dossier dossier) { //risolto questo vecchioTodo ma lascio come commento per eventuale e remota utilità futura: ERRORE: failed to lazily initialize a collection of role: com.reco.ees.gateway.repository.model.Dossier.survey: could not initialize proxy - no Session. Per risolverlo o non usare le thread OPPURE (meglio, dato che le usi altrove) aprire e chiudere la sessione in questo metodo OPPURE tutte le operazioni su DB falle in job principale (non in threads, comunque utili per avviare più operazioni di rete in parallelo). Inoltre il problema della sessione hibernate proxy (significa che non prende entità collegata ad entità richiesta) è risolto così e presente a causa del fatto che questa è una thread quindi non indagare su metodo in virtual thread future qui ma prova a fare autowired di entitymanagerfactory quando davvero pronta o converti questa platform thread in @scheduled/"@autostart" job (ma questo richiederebbe anche @Transactional per funzionare e perderebbe senso) o semplice avvio metodi di classe (controlla anche dossierresponsejob/altro) ma perdi thread quasi necessaria (quindi la migliore soluzione in questo caso è il fetch EAGER o al limite usare fetch join). Neanche Hibernate.initialize(dossier.getSurvey()) funziona.
        if(dossier.getSurvey() != null && !dossier.getSurvey().isEmpty()) { //&& Boolean.FALSE.equals(dossier.getHasSifProcessedDossier())
            SurveyInsertRequestType surveyInsertRequestType = dossierService.generateSurvey(dossier);
            String transaction = "Survey JOB";
            String logId = null;
            try {
                SurveyInsertResponseType surveyInsertResponseType;
                try {
                    dossier.setSifInteractionCount(dossier.getSifInteractionCount() + 1);
                    dossier.setLastSifInteractionDate(new Date());
                    if(dossier.getStatus().equalsIgnoreCase(DossierStatusEnum.EES_NEEDS_SURVEY.getDossierStatusEnumValue())) dossier.setStatus(DossierStatusEnum.SURVEY_SENT_KO.getDossierStatusEnumValue());
                    dossierService.save(dossier);

                    HeaderRequestType headerRequestType = dossierService.buildHeaderRequestType(dossier);
                    boolean successfulSurveyInsertResponseType;
                    if(!Boolean.parseBoolean(fakeClientsReturns) && (headerRequestType.getTimestamp() == null || headerRequestType.getTransactionID() == null)) {
                        successfulSurveyInsertResponseType = false;
                    } else {
                        logId = logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction,
                                "Requesting surveyInsert for dossier " + dossier.getIdMessage(), null);
                        surveyInsertResponseType = soapClientWfe.getSurveyInsertResponse(surveyInsertRequestType, headerRequestType, dossier.getIdMessage());
                        //String result = surveyInsertResponseType.getSurveyAckBody();
                        if(surveyInsertResponseType != null) {
                            List<String> errorCodes = new ArrayList<>();
                            if(surveyInsertResponseType.getReturnCodes() != null && surveyInsertResponseType.getReturnCodes().getErrorCodes() != null &&
                                    surveyInsertResponseType.getReturnCodes().getErrorCodes().getErrorCode() != null && !surveyInsertResponseType.getReturnCodes().getErrorCodes().getErrorCode().isEmpty()) {
                                errorCodes = surveyInsertResponseType.getReturnCodes().getErrorCodes().getErrorCode();
                                log.warn("Errors got for survey of dossier {}. Errors codes: {}", dossier.getIdMessage(), errorCodes);
                                logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, "Errors got in SurveyInsertResponse: " + String.join(",", errorCodes), null, logId);
                            }

                            if(surveyInsertResponseType.getReturnCodes() != null && surveyInsertResponseType.getReturnCodes().getWarningCodes() != null &&
                                    surveyInsertResponseType.getReturnCodes().getWarningCodes().getWarningCode() != null && !surveyInsertResponseType.getReturnCodes().getWarningCodes().getWarningCode().isEmpty()) {
                                List<String> warningCodes = surveyInsertResponseType.getReturnCodes().getWarningCodes().getWarningCode();
                                log.info("Warnings got for survey of dossier {}. Warning codes: {}", dossier.getIdMessage(), warningCodes);
                                logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "Warnings got in SurveyInsertResponse: " + String.join(",", warningCodes), null, null, logId);
                            }

                            successfulSurveyInsertResponseType = errorCodes == null || errorCodes.isEmpty() || errorCodes.stream().allMatch(String::isEmpty);
                            if(successfulSurveyInsertResponseType)
                                logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, "Completed successfully", null, logId);
                        } else successfulSurveyInsertResponseType = false;
                    }
                    dossier.setStatus(successfulSurveyInsertResponseType ? DossierStatusEnum.SURVEY_SENT_OK.getDossierStatusEnumValue() : DossierStatusEnum.SURVEY_SENT_KO.getDossierStatusEnumValue());
                    dossierService.updateDossierStatus(dossier.getIdMessage(), successfulSurveyInsertResponseType ? DossierStatusEnum.SURVEY_SENT_OK.getDossierStatusEnumValue() : DossierStatusEnum.SURVEY_SENT_KO.getDossierStatusEnumValue());
                } catch (Exception ex) {
                    logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, "Error performing getSurveyInsertResponse caused by " + ex.getMessage(), null, logId);
                    dossier.setStatus(DossierStatusEnum.SURVEY_SENT_KO.getDossierStatusEnumValue());
                    dossierService.updateDossierStatus(dossier.getIdMessage(), DossierStatusEnum.SURVEY_SENT_KO.getDossierStatusEnumValue());
                }
            } catch (Exception ex) {
                logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, "Error performing getSurveyInsertResponse caused by " + ex.getMessage(), null, logId);
                dossier.setStatus(DossierStatusEnum.SURVEY_SENT_KO.getDossierStatusEnumValue());
                dossierService.updateDossierStatus(dossier.getIdMessage(), DossierStatusEnum.SURVEY_SENT_KO.getDossierStatusEnumValue());
            }
        } else {
            logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), "Survey JOB", "No survey to send for dossier with Id: " + dossier.getIdMessage(), null);
            dossier.setSifInteractionCount(99); //valore sentinella per identificare l'assenza di questionario da inviare
            dossier.setStatus(DossierStatusEnum.SURVEY_SENT_OK.getDossierStatusEnumValue());
            dossierService.updateDossierStatus(dossier.getIdMessage(), DossierStatusEnum.SURVEY_SENT_OK.getDossierStatusEnumValue());
        }
        return dossier;
    }

    @PreDestroy
    public void shutdownExecutor() {
        log.info("Shutting down ExecutorService in SurveyJob");
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                executorService.shutdownNow();
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.error("SurveyJob ExecutorService did not terminate");
                }
            }
        } catch (InterruptedException e) {
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}