package com.reco.ees.gateway.job;

import com.reco.ees.gateway.ShutdownManager;
import com.reco.ees.gateway.enums.DossierStatusEnum;
import com.reco.ees.gateway.enums.LogScopeEnum;
import com.reco.ees.gateway.enums.ParametersEnum;
import com.reco.ees.gateway.kafka.producer.KafkaProducer;
import com.reco.ees.gateway.repository.model.Dossier;
import com.reco.ees.gateway.service.DossierService;
import com.reco.ees.gateway.service.LogService;
import com.reco.ees.gateway.service.ParameterService;
import com.reco.ees.gateway.soap_clients.SoapClientWfe;
import com.reco.ees.gateway.util.DateUtil;
import com.reco.ees.gateway.util.SoapUtils;
import com.reco.ees.gateway.util.UtilsService;
import eu.europa.schengen.ees_ns.xsd.v1.HeaderAsyncRequestType;
import eu.europa.schengen.ees_ns.xsd.v1.StartBorderControlRequestMessageType;
import jakarta.annotation.PreDestroy;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.ListIterator;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@AllArgsConstructor
public class DossierSenderJob implements Runnable {
    private final KafkaProducer kafkaProducer;
    private int maxRetries;
    @Value("${to.be.processed.record.limit:16}")
    private int recordLimit;
    @Value("${fake.clients.returns:false}")
    private String fakeClientsReturns;
    @Value("${application.name}")
    private String applicationName;

    private final UtilsService utilsService;
    private final SoapClientWfe soapClientWfe;
    private final DossierService dossierService;
    private final ParameterService parameterService;
    private final LogService logService;
    private final ExecutorService virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();
    public record DossierSenderResult(boolean success, String idMessage) {}

    @Override
    public void run() {
        log.info("Dossier Sender Job started");

        while (ShutdownManager.getIsAppRunning()) {
            ShutdownManager.incrementActiveJobs(ShutdownManager.jobNames.DossierSenderJob.name());

            String checkFrequencySeconds = parameterService.getParameterById(ParametersEnum.DOSSIER_TRANSACTION_CHECK_FREQUENCY_SECONDS.getParametersEnumValue()).getValue();
            maxRetries = Integer.parseInt(parameterService.getParameterById(ParametersEnum.DOSSIER_TRANSACTION_CHECK_MAX_RETRIES.getParametersEnumValue()).getValue());
            List<String> managedDossierStatuses = List.of(DossierStatusEnum.RECEIVED_KAFKA_DOSSIER.getDossierStatusEnumValue(), DossierStatusEnum.START_BORDER_CONTROL_KO.getDossierStatusEnumValue());
            List<Dossier> dossiers = dossierService.findDossiersByApplicationNameAndStatus(applicationName, managedDossierStatuses, recordLimit);
            dossiers.removeIf(dossier ->
                    dossier.getStatus().equalsIgnoreCase(DossierStatusEnum.START_BORDER_CONTROL_KO.getDossierStatusEnumValue()) &&
                            (
                                    (dossier.getSifInteractionCount() != null && dossier.getSifInteractionCount() >= maxRetries) ||
                                            (dossier.getLastSifInteractionDate() != null && dossier.getLastSifInteractionDate().after(DateUtil.addDeltaToDate(new Date(), 0, 0, 0, -Integer.parseInt(checkFrequencySeconds), 0)))
                            )
            );
            if(dossiers.isEmpty()) {
                utilsService.allDoneAndSleep(ShutdownManager.jobNames.DossierSenderJob.name());
                continue;
            }

            List<Future<DossierSenderResult>> futures = new ArrayList<>();
            for (Dossier dossier : dossiers) {
                futures.add(virtualThreadExecutor.submit(() -> dossierSenderThread(dossier)));
            }

            ListIterator<Future<DossierSenderResult>> iterator = futures.listIterator(futures.size());
            while (iterator.hasPrevious()) {
                Future<DossierSenderResult> future = iterator.previous();
                try {
                    DossierSenderResult result = future.get();
                    if(result.success) log.info("DossierSenderJob: Dossier with Id {} processed successfully", result.idMessage());
                    else log.warn("DossierSenderJob: Dossier with Id {} processed with problems", result.idMessage());
                } catch (Exception ex) {
                    log.error("DossierSenderJob: Error processing dossier due to: ", ex);
                }
            }

            utilsService.allDoneAndSleep(ShutdownManager.jobNames.DossierSenderJob.name());
        }
    }

    private DossierSenderResult dossierSenderThread(Dossier dossier) {
        HeaderAsyncRequestType headerAsyncRequestType = dossierService.buildHeaderAsyncRequestType(dossier);
        StartBorderControlRequestMessageType startBorderControlRequest = dossierService.mapDossierToStartBorderControlRequest(dossier);
        // check su valori null contenuti in ValidUntil, Timestamp e TransactionID serve per tener conto di possibili errori di conversione e altro generati in dossierService.buildHeaderAsyncRequestType() e dossierService.mapDossierToStartBorderControlRequest()
        if(!Boolean.parseBoolean(fakeClientsReturns) && (startBorderControlRequest.getTravelDocument().getValidUntil() == null || headerAsyncRequestType.getTimestamp() == null || headerAsyncRequestType.getTransactionID() == null)) {
            log.error("DossierSenderJob: Error processing dossier with Id: {}", dossier.getIdMessage());
            dossier.setSifInteractionCount(dossier.getSifInteractionCount() + 1);
            dossier.setLastSifInteractionDate(new Date());
            dossier.setApplication(applicationName);
            dossier.setStatus(DossierStatusEnum.START_BORDER_CONTROL_KO.getDossierStatusEnumValue());
            dossierService.save(dossier);
            if(dossier.getEgateHandled().equals(Boolean.TRUE) && dossier.getSifInteractionCount() >= maxRetries) kafkaProducer.sendAbcEgateProblemMessage(dossier);
            return new DossierSenderResult(false, dossier.getIdMessage());
        }

        boolean startBorderControlResult = false;

        dossier.setSifInteractionCount(dossier.getSifInteractionCount() + 1);
        dossier.setLastSifInteractionDate(new Date());
        dossier.setApplication(applicationName);
        if(dossier.getStatus().equalsIgnoreCase(DossierStatusEnum.RECEIVED_KAFKA_DOSSIER.getDossierStatusEnumValue())) dossier.setStatus(DossierStatusEnum.START_BORDER_CONTROL_KO.getDossierStatusEnumValue());
        dossierService.save(dossier);
        if(dossier.getEgateHandled().equals(Boolean.TRUE) && dossier.getSifInteractionCount() >= maxRetries) kafkaProducer.sendAbcEgateProblemMessage(dossier);

        String transaction = "Dossier sender JOB";
        String logId = null;
        try {
            logId = logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction,
                    "Requesting startBorderControl for dossier " + dossier.getIdMessage(), null);
            startBorderControlResult = soapClientWfe.performStartBorderControl(startBorderControlRequest, headerAsyncRequestType, dossier.getIdMessage());
            logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, startBorderControlResult ? "Completed successfully" : "Completed with errors", null, logId);
            dossier.setStatus(startBorderControlResult ? DossierStatusEnum.START_BORDER_CONTROL_OK.getDossierStatusEnumValue() : DossierStatusEnum.START_BORDER_CONTROL_KO.getDossierStatusEnumValue());
        } catch (Exception ex) {
            log.error("DossierSenderJob: Error performing startBorderControl for dossier with Id: {} due to {}", dossier.getIdMessage(), ex.getMessage());
            logService.saveEventLog(LogScopeEnum.INTERNAL.getValue(), transaction, "Error performing startBorderControl due to " + ex.getMessage(), null, logId);
            dossier.setStatus(DossierStatusEnum.START_BORDER_CONTROL_KO.getDossierStatusEnumValue());
        }

        if(startBorderControlResult) {
            dossier.setSifTransactionId(headerAsyncRequestType.getTransactionID());
            dossier.setSifInteractionCount(0);
            if (Boolean.parseBoolean(fakeClientsReturns) && dossier.getSifTransactionId() == null) dossier.setSifTransactionId(SoapUtils.generateSIFTransactionId("FAKE-NODE"));
        }

        dossierService.save(dossier);
        return new DossierSenderResult(startBorderControlResult, dossier.getIdMessage());
    }

    @PreDestroy
    public void shutdownExecutor() {
        log.info("Shutting down ExecutorService in DossierSenderJob");
        virtualThreadExecutor.shutdown();
        try {
            if (!virtualThreadExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                virtualThreadExecutor.shutdownNow();
                if (!virtualThreadExecutor.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.error("DossierSenderJob ExecutorService did not terminate");
                }
            }
        } catch (InterruptedException e) {
            virtualThreadExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
