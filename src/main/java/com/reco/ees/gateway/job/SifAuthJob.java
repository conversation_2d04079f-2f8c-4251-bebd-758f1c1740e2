package com.reco.ees.gateway.job;

import com.reco.ees.gateway.ShutdownManager;
import com.reco.ees.gateway.enums.AutoScheduledJobsLockEnum;
import com.reco.ees.gateway.enums.EmailTypeEnum;
import com.reco.ees.gateway.enums.ParametersEnum;
import com.reco.ees.gateway.enums.SifAuthStatusEnum;
import com.reco.ees.gateway.kafka.producer.KafkaProducer;
import com.reco.ees.gateway.repository.AutoScheduledJobsLockRepository;
import com.reco.ees.gateway.repository.KioskRepository;
import com.reco.ees.gateway.repository.model.AutoScheduledJobsLock;
import com.reco.ees.gateway.repository.model.Kiosk;
import com.reco.ees.gateway.service.ParameterService;
import com.reco.ees.gateway.service.SifAuthService;
import com.reco.ees.gateway.util.EmailUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;

@Slf4j
@Service
@RequiredArgsConstructor
@DependsOn({"kafkaAdmin", "kafkaProducerConfig", "kafkaConsumerConfig", "kafkaInit"})
@Order(6)
public class SifAuthJob { //SifAuthJob: sono controllato da DynamicSchedulingService
    private final ParameterService parameterService;
    private final AutoScheduledJobsLockRepository autoScheduledJobsLockRepository;
    private final SifAuthService sifAuthService;
    private final KioskRepository kioskRepository;
    private final EmailUtil emailUtil;
    private final KafkaProducer kafkaProducer;

    @Value("${using.heartbeat:false}")
    private boolean usingHeartbeat;
    @Value("${application.name}")
    private String applicationName;
    @Value("${autoscheduled.job.locks.offset.seconds:10}")
    private String locksOffsetSeconds;
    @Value("${email.sif.password.check.subject}")
    private String emailSubject;
    @Value("${email.sif.password.not.expired.content}")
    private String emailNotExpiredContent;
    @Value("${email.sif.password.expired.content}")
    private String emailExpiredContent;
    @Value("${email.days.to.password.expiration:7}")
    private long daysToPasswordExpiration;

    //@Transactional
    public synchronized void sifLogin(List<Kiosk> specificKiosks, boolean... forceCoreLogicExecution) {
        log.info("SIF Authentication job started");
        log.info("SifAuthJob running on {} thread", Thread.currentThread().isVirtual() ? "virtual" : "platform");
        if(ShutdownManager.getIsAppRunning()) {
            ShutdownManager.incrementActiveJobs(ShutdownManager.jobNames.SifAuthJob.name());
            boolean areSpecificKiosks = true;
            List<Kiosk> kiosks;
            if(specificKiosks == null || specificKiosks.isEmpty()) {
                areSpecificKiosks = false;
                kiosks = kioskRepository.findAll();
            } else kiosks = specificKiosks;
            if(kiosks.isEmpty()) {
                log.warn("SIF Authentication job: No kiosks found");
                ShutdownManager.decrementActiveJobs(ShutdownManager.jobNames.SifAuthJob.name());
                return;
            }
            if((areSpecificKiosks ? sifAuthService.shouldDoSifAuth(kiosks.toArray(new Kiosk[0])) : sifAuthService.shouldDoSifAuth()) || (forceCoreLogicExecution.length > 0 && forceCoreLogicExecution[0])) {
                AutoScheduledJobsLock autoScheduledJobsLock = null;
                try {
                    if(usingHeartbeat) Thread.sleep(Integer.parseInt(locksOffsetSeconds) * ThreadLocalRandom.current().nextLong(1000, 2000 + 1));
                    autoScheduledJobsLock = autoScheduledJobsLockRepository.findByJobName(AutoScheduledJobsLockEnum.SIF_AUTH_JOB.getAutoScheduledJobsLockEnumValue());
                    int refreshFrequencyHours = Integer.parseInt(parameterService.getParameterById(ParametersEnum.SIF_AUTHENTICATION_REFRESH_FREQUENCY_HOURS.getParametersEnumValue()).getValue());
                    LocalDateTime thresholdTime = LocalDateTime.now().minusHours(refreshFrequencyHours);
                    if (autoScheduledJobsLock == null || autoScheduledJobsLock.getLockedAt().isBefore(thresholdTime) || (forceCoreLogicExecution.length > 0 && forceCoreLogicExecution[0])) {
                    /*if(autoScheduledJobsLock != null && !autoScheduledJobsLock.getApplication().equalsIgnoreCase(applicationName)) {
                        log.info("Lock expired for SifAuthJob");
                        autoScheduledJobsLockRepository.delete(autoScheduledJobsLock);
                    }*/
                        autoScheduledJobsLock = new AutoScheduledJobsLock();
                        autoScheduledJobsLock.setApplication(applicationName);
                        autoScheduledJobsLock.setJobName(AutoScheduledJobsLockEnum.SIF_AUTH_JOB.getAutoScheduledJobsLockEnumValue());
                        autoScheduledJobsLock.setLockedAt(LocalDateTime.now());
                        autoScheduledJobsLockRepository.saveAndFlush(autoScheduledJobsLock);
                    } else {
                        log.info("Lock already exists and is not expired for SifAuthJob");
                        ShutdownManager.decrementActiveJobs(ShutdownManager.jobNames.SifAuthJob.name());
                        return;
                    }
                } catch (Exception e) {
                    log.info("Contention lost by {} for SifAuthJob", applicationName);
                    ShutdownManager.decrementActiveJobs(ShutdownManager.jobNames.SifAuthJob.name());
                    return;
                }
                List<Kiosk> listEgate = kiosks.stream().filter(kiosk -> kiosk.getIsEgate() != null && kiosk.getIsEgate()).toList();
                List<Kiosk> listSSK = kiosks.stream().filter(kiosk -> kiosk.getIsEgate() == null || !kiosk.getIsEgate()).toList();
                log.info("SIF Authentication job started by {}", applicationName);
                try {
                    if(!listSSK.isEmpty()) {
                        boolean result = sifAuthService.performLoginForKiosks(listSSK, false);
                        //TODO: cosa fase se result risulta false? Significa che non abbiamo fatto login praticamente. Credo che non dobbiamo/possiamo fare nulla o al limite inviare email in azienda (DA DECIDERE, NON FARE NULLA!)
                        /*if(!result) listSSK.forEach(ssk -> {
                            log.error("SIF Authentication job failed for Self Service Kiosk {}", ssk.getIdKiosk());
                            kafkaProducer.sendAbcEgateProblemMessage(ssk);
                        });*/
                    }
                } catch (Exception e) {
                    log.error("Error during SIF Authentication job for Self Service Kiosks", e);
                    sifAuthService.updateSifAuthStatus(applicationName, SifAuthStatusEnum.AUTH_KO.getSifAuthStatusEnumValue(), areSpecificKiosks ? kiosks : null);
                }
                try {
                    if(!listEgate.isEmpty()) {
                        boolean result = sifAuthService.performLoginForKiosks(listEgate, true);
                        if(!result) listEgate.forEach(egate -> {
                            log.error("SIF Authentication job failed for eGate {}", egate.getIdKiosk());
                            kafkaProducer.sendAbcEgateProblemMessage(egate);
                        });
                    }
                } catch (Exception e) {
                    log.error("Error during SIF Authentication job for ABC e-Gates", e);
                    sifAuthService.updateSifAuthStatus(applicationName, SifAuthStatusEnum.AUTH_KO.getSifAuthStatusEnumValue(), areSpecificKiosks ? kiosks : null);
                }
                autoScheduledJobsLockRepository.delete(autoScheduledJobsLock);
                log.info("SIF Authentication job finished by {}", applicationName);
                ShutdownManager.decrementActiveJobs(ShutdownManager.jobNames.SifAuthJob.name());
            } else {
                log.info("SifAuthJob skipped by {}", applicationName);
            }
        } else {
            log.info("SifAuthJob skipped by {}", applicationName);
        }

        checkSifPasswordExpiration();

        ShutdownManager.decrementActiveJobs(ShutdownManager.jobNames.SifAuthJob.name());
    }

    public void checkSifPasswordExpiration() {
        log.info("SIF password check started");
        /*Date passwordExpirationDate = sifAuthService.getSifPasswordExpirationDate();
        if(passwordExpirationDate != null) {
            long daysToExpiration = ChronoUnit.DAYS.between(LocalDate.now(), Objects.requireNonNull(passwordExpirationDate).toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
            if (daysToExpiration <= daysToPasswordExpiration) {
                String sifUser = parameterService.getParameterById(ParametersEnum.SIF_AUTHENTICATION_USER_KIOSK.getParametersEnumValue()).getValue();
                String content = daysToExpiration < 0 ? emailExpiredContent : emailNotExpiredContent;
                content = content.replace("XXX", sifUser);
                content = content.replace("YYY", passwordExpirationDate.toString());
                content = content.replace("ZZZ", String.valueOf(daysToExpiration < 0 ? -daysToExpiration : daysToExpiration));
                emailUtil.sendEmail(EmailTypeEnum.SIF_PASSWORD_EXPIRATION, emailSubject, content);
            }
        }*/
        List<Object[]> allPasswordsExpirationDates = sifAuthService.getAllSifPasswordsExpirationDates(); //in teoria saranno sempre al più 2 elementi, uno per e-gate e uno per kiosk
        if(allPasswordsExpirationDates != null && !allPasswordsExpirationDates.isEmpty()) {
            allPasswordsExpirationDates.forEach(passwordExpirationDate -> {
                long daysToExpiration = ChronoUnit.DAYS.between(LocalDate.now(), Objects.requireNonNull((Date) passwordExpirationDate[0]).toInstant().atZone(ZoneId.systemDefault()).toLocalDate());
                if (daysToExpiration <= daysToPasswordExpiration) {
                    String sifUser = passwordExpirationDate[1].equals(Boolean.TRUE) ? parameterService.getParameterById(ParametersEnum.SIF_AUTHENTICATION_USER_EGATE.getParametersEnumValue()).getValue() : parameterService.getParameterById(ParametersEnum.SIF_AUTHENTICATION_USER_KIOSK.getParametersEnumValue()).getValue();
                    String content = daysToExpiration < 0 ? emailExpiredContent : emailNotExpiredContent;
                    content = content.replace("XXX", sifUser);
                    content = content.replace("YYY", passwordExpirationDate[0].toString());
                    content = content.replace("ZZZ", String.valueOf(daysToExpiration < 0 ? -daysToExpiration : daysToExpiration));
                    emailUtil.sendEmail(EmailTypeEnum.SIF_PASSWORD_EXPIRATION, emailSubject, content);
                }
            });
        }

        log.info("SIF password check finished");
    }
}
