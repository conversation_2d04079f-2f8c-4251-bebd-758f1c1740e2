package com.reco.ees.gateway.repository.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.UuidGenerator;

import java.util.Date;

@Getter
@Setter
@Entity(name = "SifAuth")
@Table(name = "sif_auth", indexes = {
        @Index(name = "idx_sifauth_application_appstatus", columnList = "application, appstatus"), // Combinato per queries su entrambi
        @Index(name = "idx_sifauth_id_kiosk", columnList = "id_kiosk", unique = true), // Gia' unico via @JoinColumn, ma esplicito indice per sicurezza
        @Index(name = "idx_sifauth_last_login_date_time", columnList = "lastLoginDateTime"),
        @Index(name = "idx_sifauth_password_expiration_date", columnList = "passwordExpirationDate"),
        @Index(name = "idx_sifauth_appstatus", columnList = "appstatus") // non indispensabile
})
//@jakarta.persistence.Cacheable // JPA standard
@Cache(usage = CacheConcurrencyStrategy.NONSTRICT_READ_WRITE, region = "sifAuthCache") //CacheConcurrencyStrategy.READ_WRITE
public class SifAuth {
    @Id
    @UuidGenerator
    private String idSifAuth;
    private Date dateOfExpire;
    private Date lastLoginDateTime;
    private Integer maxInvalidPasswordAttempts;
    private String message;
    private Date passwordExpirationDate;
    private String status;
    private String username;

    private String application;
    private String appstatus;

    @Column(columnDefinition = "TEXT")
    private String token;

    @OneToOne
    @JoinColumn(name = "id_kiosk", unique = true)
    private Kiosk kiosk;
}
    