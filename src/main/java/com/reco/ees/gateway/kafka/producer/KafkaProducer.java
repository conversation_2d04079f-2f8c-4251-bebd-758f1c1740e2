package com.reco.ees.gateway.kafka.producer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.reco.ees.gateway.enums.AbcEgateMessageEssentialField;
import com.reco.ees.gateway.enums.AbcEgateMessageTypeEnum;
import com.reco.ees.gateway.enums.AbcEgateResponseMessageResultEnum;
import com.reco.ees.gateway.job.CertificatesJob;
import com.reco.ees.gateway.job.LuoghiAndDocumentiSDIJob;
import com.reco.ees.gateway.kafka.model.AbcEgateOperazioniAtomicheMessage;
import com.reco.ees.gateway.kafka.model.AbcEgateProblemMessage;
import com.reco.ees.gateway.kafka.model.AbcEgateResponseMessage;
import com.reco.ees.gateway.kafka.model.RegistrationMessage;
import com.reco.ees.gateway.repository.KioskRepository;
import com.reco.ees.gateway.repository.model.Dossier;
import com.reco.ees.gateway.repository.model.Kiosk;
import com.reco.ees.gateway.service.CertificateRequestStatusService;
import com.reco.ees.gateway.service.SDIDownloadRequestStatusService;
import com.reco.ees.gateway.util.UtilsService;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.support.SendResult;
import org.springframework.stereotype.Service;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

import static com.reco.ees.gateway.job.CertificatesJob.certificateSentStatusMap;
import static com.reco.ees.gateway.job.LuoghiAndDocumentiSDIJob.SDIDownloadSentStatusMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class KafkaProducer {
    private final CertificateRequestStatusService certificateRequestStatusService;
    private final SDIDownloadRequestStatusService sDIDownloadRequestStatusService;
    @Value("${kafka.sent.message.show:false}")
    private boolean showSentMessage;
    @Value("${kafka.node.abc.response.topicPattern}")
    private String kafkaNodeAbcResponseTopicPattern;
    @Value("${kafka.node.abc.process.topicPattern}")
    private String kafkaNodeAbcProcessTopicPattern;
    @Value("${kafka.certificates.topicName}")
    private String certificatesTopicName;
    @Value("${kafka.sdi.download.topicName}")
    private String sdiTopicName;
    @Value("${fake.kafka.registration.quantity:100}")
    private long fakeKafkaRegistrationQuantity;
    @Value("${kafka.registration.topicName}")
    private String registrationTopicName;
    @Getter
    public static final String unknownFieldValue = "unknown";

    private final KafkaTemplate<String, String> kafkaTemplate;
    private final UtilsService utilsService;
    private final KioskRepository kioskRepository;
    private final ObjectMapper objectMapper;

    public CompletableFuture<SendResult<String, String>> sendMessage(String topic, Object message) {
        try {
            String serializedMessage = utilsService.serializeMessage(message);
            // log.debug("Sending message to topic {}: {}", topic, serializedMessage);
            return kafkaTemplate.send(topic, serializedMessage);
        } catch (Exception ex) {
            log.error("Failed to send message to topic {}: {}", topic, message, ex);
            // throw new KafkaProductionException("Failed to send message", ex);
            return CompletableFuture.failedFuture(ex);
        }
    }


    public void sendAbcEgateProblemMessage(Exception ex, Map<AbcEgateMessageEssentialField, String> metadata) {
        AbcEgateProblemMessage abcEgateProblemMessage = new AbcEgateProblemMessage();
        Kiosk egate = kioskRepository.findById(metadata != null ? metadata.get(AbcEgateMessageEssentialField.NODE_ID) : unknownFieldValue).orElse(null);
        abcEgateProblemMessage.setId(UUID.randomUUID().toString());
        abcEgateProblemMessage.setNodeId(metadata != null ? metadata.get(AbcEgateMessageEssentialField.NODE_ID) : unknownFieldValue);
        abcEgateProblemMessage.setTimestamp(new Date().toString());
        abcEgateProblemMessage.setPassengerId(metadata != null ? metadata.get(AbcEgateMessageEssentialField.PASSENGER_ID) : unknownFieldValue);
        abcEgateProblemMessage.setType(AbcEgateMessageTypeEnum.PROBLEM.getAbcEgateMessageTypeEnumValue());
        abcEgateProblemMessage.setHas_to_be_processed("False");
        abcEgateProblemMessage.setIdChiamante(metadata != null ? metadata.get(AbcEgateMessageEssentialField.ID_CHIAMANTE) : unknownFieldValue);
        abcEgateProblemMessage.setNodeName(egate != null ? egate.getIdMachine() : "EES-GATEWAY");
        abcEgateProblemMessage.setIs_entry_node(metadata != null ? metadata.get(AbcEgateMessageEssentialField.IS_ENTRY_NODE) : unknownFieldValue);
        AbcEgateProblemMessage.Content content = new AbcEgateProblemMessage.Content();
        content.setData("True");
        abcEgateProblemMessage.setContent(content);

        String abcEgateProblemMessageSerialized = null;
        try {
            abcEgateProblemMessageSerialized = objectMapper.writeValueAsString(abcEgateProblemMessage);
        } catch (JsonProcessingException e) {
            abcEgateProblemMessageSerialized = abcEgateProblemMessage.getId();
        }
        try {
            String nodeIdForTopic = metadata != null ? metadata.getOrDefault(AbcEgateMessageEssentialField.NODE_ID, unknownFieldValue) : unknownFieldValue;
            if (nodeIdForTopic.equals(unknownFieldValue)) {
                nodeIdForTopic = "unknown_node";
            }
            String topicName = kafkaNodeAbcProcessTopicPattern.replace("*", nodeIdForTopic);

            String finalAbcEgateProblemMessageSerialized = abcEgateProblemMessageSerialized;
            sendMessage(topicName, abcEgateProblemMessage)
                    .thenAccept(kafkaResult -> {
                        log.info("{}: sent message=[{}] with offset=[{}]", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? finalAbcEgateProblemMessageSerialized : "", kafkaResult.getRecordMetadata().offset()); //"AbcEgateConsumer.sendAbcEgateProblemMessage"
                    })
                    .exceptionally(exception -> {
                        log.error("{}: unable to send message=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? finalAbcEgateProblemMessageSerialized : "", ex.getMessage() + " - " + exception.getMessage());
                        return null;
                    });
        } catch (Exception e) {
            log.error("{}: unable to send message with id=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? abcEgateProblemMessageSerialized : "", e.getMessage());
        }
    }

    public void sendAbcEgateProblemMessage(Dossier dossier) {
        AbcEgateProblemMessage abcEgateProblemMessage = new AbcEgateProblemMessage();
        abcEgateProblemMessage.setId(UUID.randomUUID().toString());
        abcEgateProblemMessage.setNodeId(dossier.getNodeId());
        abcEgateProblemMessage.setTimestamp(new Date().toString());
        abcEgateProblemMessage.setPassengerId(dossier.getPassengerId());
        abcEgateProblemMessage.setType(AbcEgateMessageTypeEnum.PROBLEM.getAbcEgateMessageTypeEnumValue());
        abcEgateProblemMessage.setHas_to_be_processed("False");
        abcEgateProblemMessage.setIdChiamante(dossier.getIdChiamante());
        Kiosk egate = kioskRepository.findById(dossier.getNodeId()).orElse(null);
        abcEgateProblemMessage.setNodeName(egate != null ? egate.getIdMachine() : "EES-GATEWAY");
        abcEgateProblemMessage.setIs_entry_node(egate != null ? String.valueOf(egate.getIsEntryEgate()) : "");
        AbcEgateProblemMessage.Content content = new AbcEgateProblemMessage.Content();
        content.setData("True");
        abcEgateProblemMessage.setContent(content);

        String abcEgateProblemMessageSerialized = null;
        try {
            abcEgateProblemMessageSerialized = objectMapper.writeValueAsString(abcEgateProblemMessage);
        } catch (JsonProcessingException e) {
            abcEgateProblemMessageSerialized = abcEgateProblemMessage.getId();
        }
        try {
            String nodeIdForTopic = dossier.getNodeId() != null ? dossier.getNodeId() : unknownFieldValue;
            if (nodeIdForTopic.equals(unknownFieldValue)) {
                nodeIdForTopic = "unknown_node";
            }
            String topicName = kafkaNodeAbcProcessTopicPattern.replace("*", nodeIdForTopic);

            String finalAbcEgateProblemMessageSerialized = abcEgateProblemMessageSerialized;
            sendMessage(topicName, abcEgateProblemMessage)
                    .thenAccept(kafkaResult -> {
                        log.info("{}: sent message=[{}] with offset=[{}]", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? finalAbcEgateProblemMessageSerialized : "", kafkaResult.getRecordMetadata().offset()); //"DossierResponseJob.sendAbcEgateProblemMessage"
                    })
                    .exceptionally(exception -> {
                        log.error("{}: unable to send message=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? finalAbcEgateProblemMessageSerialized : "", exception.getMessage());
                        return null;
                    });
        } catch (Exception e) {
            log.error("{}: unable to send message with id=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? abcEgateProblemMessageSerialized : "", e.getMessage());
        }
    }

    public void sendAbcEgateProblemMessage(Kiosk kioskOrEgate) {
        AbcEgateProblemMessage abcEgateProblemMessage = new AbcEgateProblemMessage();
        abcEgateProblemMessage.setId(UUID.randomUUID().toString());
        abcEgateProblemMessage.setNodeId(kioskOrEgate.getIdKiosk());
        abcEgateProblemMessage.setTimestamp(new Date().toString());
        abcEgateProblemMessage.setPassengerId("");
        abcEgateProblemMessage.setType(AbcEgateMessageTypeEnum.PROBLEM.getAbcEgateMessageTypeEnumValue());
        abcEgateProblemMessage.setHas_to_be_processed("False");
        abcEgateProblemMessage.setIdChiamante(kioskOrEgate.getIdMachine());
        abcEgateProblemMessage.setNodeName(kioskOrEgate.getIdMachine());
        abcEgateProblemMessage.setIs_entry_node(String.valueOf(kioskOrEgate.getIsEntryEgate()));
        AbcEgateProblemMessage.Content content = new AbcEgateProblemMessage.Content();
        content.setData("True");
        abcEgateProblemMessage.setContent(content);

        String abcEgateProblemMessageSerialized = null;
        try {
            abcEgateProblemMessageSerialized = objectMapper.writeValueAsString(abcEgateProblemMessage);
        } catch (JsonProcessingException e) {
            abcEgateProblemMessageSerialized = abcEgateProblemMessage.getId();
        }
        try {
            String nodeIdForTopic = kioskOrEgate.getIdKiosk() != null ?  kioskOrEgate.getIdKiosk() : unknownFieldValue;
            if (nodeIdForTopic.equals(unknownFieldValue)) {
                nodeIdForTopic = "unknown_node";
            }
            String topicName = kafkaNodeAbcProcessTopicPattern.replace("*", nodeIdForTopic);

            String finalAbcEgateProblemMessageSerialized = abcEgateProblemMessageSerialized;
            sendMessage(topicName, abcEgateProblemMessage)
                    .thenAccept(kafkaResult -> {
                        log.info("{}: sent message=[{}] with offset=[{}]", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? finalAbcEgateProblemMessageSerialized : "", kafkaResult.getRecordMetadata().offset()); //"DossierResponseJob.sendAbcEgateProblemMessage"
                    })
                    .exceptionally(exception -> {
                        log.error("{}: unable to send message=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? finalAbcEgateProblemMessageSerialized : "", exception.getMessage());
                        return null;
                    });
        } catch (Exception e) {
            log.error("{}: unable to send message with id=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? abcEgateProblemMessageSerialized : "", e.getMessage());
        }
    }

    public void sendAbcEgateResponseMessage(Dossier dossier, AbcEgateResponseMessageResultEnum result, String details) {
        AbcEgateResponseMessage abcEgateResponseMessage = new AbcEgateResponseMessage();
        abcEgateResponseMessage.setPassengerId(dossier.getCountryIssue().concat(dossier.getDocumentNumber()));
        abcEgateResponseMessage.setResult(result.getAbcEgateResponseMessageResultEnumValue());
        abcEgateResponseMessage.setDetails(details);
        try {
            sendMessage(kafkaNodeAbcResponseTopicPattern.replace("*", dossier.getNodeId()), abcEgateResponseMessage)
                    .thenAccept(kafkaResult -> {
                        log.info("{}: sent message=[{}] with offset=[{}]", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? abcEgateResponseMessage : "", kafkaResult.getRecordMetadata().offset()); //sendAbcEgateResponseMessage
                    })
                    .exceptionally(ex -> {
                        log.error("{}: unable to send message=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? abcEgateResponseMessage : "", ex.getMessage());
                        return null;
                    });
        } catch (Exception e) {
            log.error("{}: unable to send message=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? abcEgateResponseMessage : "", e.getMessage());
        }
    }

    public void sendAbcEgateOperazioniAtomicheMessage(Dossier dossier, Boolean isAtomicOperationRequiredInVIS) {
        AbcEgateOperazioniAtomicheMessage abcEgateOperazioniAtomicheMessage = new AbcEgateOperazioniAtomicheMessage();
        abcEgateOperazioniAtomicheMessage.setId(UUID.randomUUID().toString());
        abcEgateOperazioniAtomicheMessage.setNodeId(dossier.getNodeId());
        Kiosk egate = kioskRepository.findById(dossier.getNodeId()).orElse(null);
        abcEgateOperazioniAtomicheMessage.setNodeName(egate != null ? egate.getIdMachine() : "EES-GATEWAY");
        abcEgateOperazioniAtomicheMessage.setIdChiamante(dossier.getIdChiamante());
        abcEgateOperazioniAtomicheMessage.setTimestamp(new Date().toString());
        abcEgateOperazioniAtomicheMessage.setPassengerId(dossier.getPassengerId());
        abcEgateOperazioniAtomicheMessage.setType(AbcEgateMessageTypeEnum.OPERAZIONI_ATOMICHE.getAbcEgateMessageTypeEnumValue());
        abcEgateOperazioniAtomicheMessage.setHas_to_be_processed("False");
        abcEgateOperazioniAtomicheMessage.setIs_entry_node(egate != null ? String.valueOf(egate.getIsEntryEgate()) : "");
        AbcEgateOperazioniAtomicheMessage.Content content = new AbcEgateOperazioniAtomicheMessage.Content();
        content.setData("True");
        content.setInVis(String.valueOf(isAtomicOperationRequiredInVIS));
        abcEgateOperazioniAtomicheMessage.setContent(content);

        String abcEgateOperazioniAtomicheMessageSerialized = null;
        try {
            abcEgateOperazioniAtomicheMessageSerialized = objectMapper.writeValueAsString(abcEgateOperazioniAtomicheMessage);
        } catch (JsonProcessingException e) {
            abcEgateOperazioniAtomicheMessageSerialized = abcEgateOperazioniAtomicheMessage.getId();
        }
        try {
            String nodeIdForTopic = dossier.getNodeId() != null ? dossier.getNodeId() : unknownFieldValue;
            if (nodeIdForTopic.equals(unknownFieldValue)) {
                nodeIdForTopic = "unknown_node";
            }
            String topicName = kafkaNodeAbcProcessTopicPattern.replace("*", nodeIdForTopic);

            String finalAbcEgateOperazioniAtomicheMessageSerialized = abcEgateOperazioniAtomicheMessageSerialized;
            sendMessage(topicName, abcEgateOperazioniAtomicheMessage)
                    .thenAccept(kafkaResult -> {
                        log.info("{}: sent message=[{}] with offset=[{}]", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? finalAbcEgateOperazioniAtomicheMessageSerialized : "", kafkaResult.getRecordMetadata().offset()); //sendAbcEgateOperazioniAtomicheMessage
                    })
                    .exceptionally(exception -> {
                        log.error("{}: unable to send message=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? finalAbcEgateOperazioniAtomicheMessageSerialized : "", exception.getMessage());
                        return null;
                    });
        } catch (Exception e) {
            log.error("{}: unable to send message with id=[{}] due to : {}", utilsService.getCurrentAndCallerMethodInfo(), showSentMessage ? abcEgateOperazioniAtomicheMessageSerialized : "", e.getMessage());
        }
    }

    public void sendToKafkaAndSaveCertificateRequestStatus(String ldif, CertificatesJob.CertificateTypeEnum certificateTypeEnum) {
        if(ldif == null) return;

        ObjectNode certificateJsonMessage = objectMapper.createObjectNode();
        certificateJsonMessage.put("type", certificateTypeEnum.toString());
        certificateJsonMessage.put("content", ldif);

        try {
            sendMessage(certificatesTopicName, certificateJsonMessage)
                    .thenAccept(result -> {
                        //if(certificateStatusEnum.equals(CertificateStatusEnum.DS_AND_RL_CERTIFICATE_SENT_ON_KAFKA_QUEUE)) log.info("AAA 5 - INVIATI CORRETTAMENTE A KAFKA");
                        log.info("{}: sent message=[{}] with offset=[{}]", certificateTypeEnum.toString(), showSentMessage ? ldif : "", result.getRecordMetadata().offset());
                        certificateRequestStatusService.saveOrUpdateCertificateRequestStatus(certificateSentStatusMap.get(certificateTypeEnum).getCertificateStatusEnumValue());
                    })
                    .exceptionally(ex -> {
                        //log.info("AAA 5.1 - ERRORE IN INVIO A KAFKA");
                        log.error("{}: unable to send message=[{}] due to : {}", certificateTypeEnum.toString(), showSentMessage ? ldif : "", ex.getMessage());
                        return null;
                    });
        } catch (Exception e) {
            log.error("{}: unable to send message=[{}] due to : {}", certificateTypeEnum.toString(), showSentMessage ? ldif : "", e.getMessage());
        }
    }
    /*private CompletableFuture<CertificateResult> sendToKafkaAndSaveCertificateRequestStatus(String ldif, CertificateStatusEnum certificateStatusEnum) { //VERSIONE ALTERNATIVA CHE ATTENDE L'EFFETTIVO INVIO (non solo la richiesta async di invio) SU KAFKA PRIMA DI SCRIVERE IL RECORD CERTIFICATE_JOB_COMPLETED_OK
        if (ldif == null) return CompletableFuture.completedFuture(new CertificateResult(false, certificateStatusEnum));
        return kafkaProducer.sendMessage(topicName, ldif)
                .thenApply(result -> {
                    log.info("Sent message=[" + (showSentMessage ? ldif : "") + "] with offset=[" + result.getRecordMetadata().offset() + "]");
                    certificateRequestStatusService.saveOrUpdateCertificateRequestStatus(certificateStatusEnum.getCertificateStatusEnumValue());
                    return new CertificateResult(true, certificateStatusEnum);
                })
                .exceptionally(ex -> {
                    log.error("Unable to send message=[" + (showSentMessage ? ldif : "") + "] due to : " + ex.getMessage());
                    return new CertificateResult(false, certificateStatusEnum);
                });
    }*/

    public void sendToKafkaAndSaveDownloadSDIStatus(Object SDIDownload, LuoghiAndDocumentiSDIJob.DownloadSDITypeEnum downloadSDITypeEnum) {
        if(SDIDownload == null) return;
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSSSS"));
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        /*objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.NONE);
        objectMapper.setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);

        objectMapper.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.NONE);
        objectMapper.setVisibility(PropertyAccessor.FIELD, JsonAutoDetect.Visibility.ANY);
        objectMapper.setVisibility(PropertyAccessor.GETTER, JsonAutoDetect.Visibility.NONE);
        objectMapper.setVisibility(PropertyAccessor.IS_GETTER, JsonAutoDetect.Visibility.NONE);
        objectMapper.configure(MapperFeature.USE_ANNOTATIONS, true);*/

        //objectMapper.setPropertyNamingStrategy(PropertyNamingStrategies.UPPER_CAMEL_CASE);
        /*objectMapper.configure(MapperFeature.USE_ANNOTATIONS, true);
        objectMapper.setPropertyNamingStrategy(new PropertyNamingStrategy() {
            @Override
            public String nameForField(MapperConfig<?> config, AnnotatedField field, String defaultName) {
                return field.getName();
            }

            @Override
            public String nameForGetterMethod(MapperConfig<?> config, AnnotatedMethod method, String defaultName) {
                return method.getName();
            }

            @Override
            public String nameForSetterMethod(MapperConfig<?> config, AnnotatedMethod method, String defaultName) {
                return method.getName();
            }
        });*/
        ObjectNode SDIDownloadJsonMessage = objectMapper.createObjectNode();
        SDIDownloadJsonMessage.put("type", downloadSDITypeEnum.toString());
        /*SDIDownloadJsonMessage.put("content", SDIDownload);*/
        JsonNode contentNode = objectMapper.valueToTree(SDIDownload);
        SDIDownloadJsonMessage.set("content", contentNode);
        try {
            sendMessage(sdiTopicName, SDIDownloadJsonMessage)
                    .thenAccept(result -> {
                        log.info("{}: sent message=[{}] with offset=[{}]", downloadSDITypeEnum.toString(), showSentMessage ? SDIDownload : "", result.getRecordMetadata().offset());
                        sDIDownloadRequestStatusService.saveOrUpdateSDIDownloadRequestStatus(SDIDownloadSentStatusMap.get(downloadSDITypeEnum).getLuoghiAndDocumentiSDIStatusEnumValue());
                    })
                    .exceptionally(ex -> {
                        //log.info("AAA 5.1 - ERRORE IN INVIO A KAFKA");
                        log.error("{}: unable to send message=[{}] due to : {}", downloadSDITypeEnum.toString(), showSentMessage ? SDIDownload : "", ex.getMessage());
                        return null;
                    });
        } catch (Exception e) {
            log.error("{}: unable to send message=[{}] due to : {}", downloadSDITypeEnum.toString(), showSentMessage ? SDIDownload : "", e.getMessage());
        }
    }

    public void produceFakeKafkaRegistrationMessages() throws IOException {
        ObjectMapper objectMapper = new ObjectMapper();
        RegistrationMessage referenceRegistrationMessage;

        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream("kafkaExamples/registration_example.json")) {
            if (inputStream == null) {
                throw new FileNotFoundException("File not found: kafkaExamples/registration_example.json");
            }
            referenceRegistrationMessage = objectMapper.readValue(inputStream, RegistrationMessage.class);
        }

        for (int ii = 1; ii <= fakeKafkaRegistrationQuantity; ii++) {
            referenceRegistrationMessage.setId(utilsService.generateOrderedUUID());
            sendMessage(registrationTopicName, referenceRegistrationMessage);
        }
        log.info("Produced {} fake kafka registration messages", fakeKafkaRegistrationQuantity);
    }

}
