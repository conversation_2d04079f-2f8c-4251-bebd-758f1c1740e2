package com.reco.ees.gateway.kafka.model;

import lombok.Data;

@Data
public class AbcEgateOperazioniAtomicheMessage {
    private String id;
    private String nodeId;
    private String nodeName;
    private String idChiamante;
    private String timestamp;
    private String passengerId;
    private String type;
    private String has_to_be_processed;
    private String is_entry_node;
    private Content content;

    @Data
    public static class Content {
        private String data;
        private String inVis;
    }
}
