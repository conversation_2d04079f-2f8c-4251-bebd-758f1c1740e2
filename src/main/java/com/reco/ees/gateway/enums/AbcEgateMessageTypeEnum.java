package com.reco.ees.gateway.enums;

public enum AbcEgateMessageTypeEnum {
    DOCUMENT("DOCUMENT"),
    FACE("FACE"),
    FINGERPRINT("FINGERPRINT"),
    QUEUE_ALERT("QUEUE_ALERT"),
    PANIC_ALERT("PANIC_ALERT"),
    PERFORM_ENDBORDERCONTROL("PERFORM_ENDBORDERCONTROL"),
    ENDED_PROCESS("ENDED_PROCESS"),
    PROBLEM("PROBLEM"),
    OPERAZIONI_ATOMICHE("OPERAZIONI_ATOMICHE");

    private final String value;

    AbcEgateMessageTypeEnum(String value) {
        this.value = value;
    }

    public String getAbcEgateMessageTypeEnumValue() {
        return value;
    }
}
