<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<bindings version="3.0" xmlns="https://jakarta.ee/xml/ns/jaxb">
  <!--

Questo file è stato generato dall'Eclipse Implementation of JAXB, v4.0.5 
Vedere https://eclipse-ee4j.github.io/jaxb-ri 
Qualsiasi modifica a questo file andrà persa durante la ricompilazione dello schema di origine. 
Generato il: 2025.06.17 alle 03:09:06 PM CEST
  -->
  <bindings scd="x-schema::tns" if-exists="true" auto-acknowledge="true" xmlns:tns="http://www.interno.it/schengen/vis/webservice/csm">
    <schemaBindings map="false">
      <package name="com.reco.ees.gateway.generated.sif.iae"/>
    </schemaBindings>
    <bindings scd="tns:request" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.Request"/>
    </bindings>
  </bindings>
  <bindings scd="x-schema::tns" if-exists="true" auto-acknowledge="true" xmlns:tns="http://sifiiegate.dcft.interno.it">
    <schemaBindings map="false">
      <package name="com.reco.ees.gateway.generated.sif.iae"/>
    </schemaBindings>
    <bindings scd="tns:LuoghiSDI" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.LuoghiSDI"/>
    </bindings>
    <bindings scd="tns:LuoghiSDIResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.LuoghiSDIResponse"/>
    </bindings>
    <bindings scd="tns:DocumentiSDI" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.DocumentiSDI"/>
    </bindings>
    <bindings scd="tns:DocumentiSDIResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.DocumentiSDIResponse"/>
    </bindings>
    <bindings scd="~tns:EsitoType" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.EsitoType"/>
    </bindings>
  </bindings>
  <bindings scd="x-schema::tns" if-exists="true" auto-acknowledge="true" xmlns:tns="http://tempuri.org/">
    <schemaBindings map="false">
      <package name="com.reco.ees.gateway.generated.sif.iae"/>
    </schemaBindings>
    <bindings scd="tns:SIFServizioBCS" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SIFServizioBCS"/>
    </bindings>
    <bindings scd="~tns:BCSAlertRequest" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.BCSAlertRequest"/>
    </bindings>
    <bindings scd="tns:SIFServizioBCSResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SIFServizioBCSResponse"/>
    </bindings>
    <bindings scd="tns:SendMipgRequest" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendMipgRequest"/>
    </bindings>
    <bindings scd="~tns:ArrayOfBoolean" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.ArrayOfBoolean"/>
    </bindings>
    <bindings scd="tns:SendMipgRequestResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendMipgRequestResponse"/>
    </bindings>
    <bindings scd="~tns:MIPGResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.MIPGResponse"/>
    </bindings>
    <bindings scd="tns:SendSDIRequest" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendSDIRequest"/>
    </bindings>
    <bindings scd="tns:SendSDIRequestResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendSDIRequestResponse"/>
    </bindings>
    <bindings scd="~tns:IAEResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.IAEResponse"/>
    </bindings>
    <bindings scd="tns:SendIVISCheckRequest" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendIVISCheckRequest"/>
    </bindings>
    <bindings scd="tns:SendIVISCheckRequestResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendIVISCheckRequestResponse"/>
    </bindings>
    <bindings scd="~tns:IVISCheckResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.IVISCheckResponse"/>
    </bindings>
    <bindings scd="tns:SendIVISCheckRequestNew" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendIVISCheckRequestNew"/>
    </bindings>
    <bindings scd="~tns:IVISCheckRequest" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.IVISCheckRequest"/>
    </bindings>
    <bindings scd="tns:SendIVISCheckRequestNewResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendIVISCheckRequestNewResponse"/>
    </bindings>
    <bindings scd="tns:SendIVISCheckRequest3" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendIVISCheckRequest3"/>
    </bindings>
    <bindings scd="~tns:IVISCheckRequest3" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.IVISCheckRequest3"/>
    </bindings>
    <bindings scd="tns:SendIVISCheckRequest3Response" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendIVISCheckRequest3Response"/>
    </bindings>
    <bindings scd="~tns:IVISCheckResponse3" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.IVISCheckResponse3"/>
    </bindings>
    <bindings scd="tns:SendIVISIssueRequest" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendIVISIssueRequest"/>
    </bindings>
    <bindings scd="~tns:IVISIssueRequest" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.IVISIssueRequest"/>
    </bindings>
    <bindings scd="tns:SendIVISIssueRequestResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendIVISIssueRequestResponse"/>
    </bindings>
    <bindings scd="~tns:IVISIssueResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.IVISIssueResponse"/>
    </bindings>
    <bindings scd="tns:SendIVISIssueRequestNew" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendIVISIssueRequestNew"/>
    </bindings>
    <bindings scd="tns:SendIVISIssueRequestNewResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendIVISIssueRequestNewResponse"/>
    </bindings>
    <bindings scd="tns:BCSRequest" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.BCSRequest"/>
    </bindings>
    <bindings scd="tns:BCSRequestResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.BCSRequestResponse"/>
    </bindings>
    <bindings scd="~tns:SIFResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SIFResponse"/>
    </bindings>
    <bindings scd="tns:CheckStranieriWeb" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.CheckStranieriWeb"/>
    </bindings>
    <bindings scd="tns:CheckStranieriWebResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.CheckStranieriWebResponse"/>
    </bindings>
    <bindings scd="~tns:SIFStranieriWebResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SIFStranieriWebResponse"/>
    </bindings>
    <bindings scd="tns:SignTAChallenge" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SignTAChallenge"/>
    </bindings>
    <bindings scd="~tns:RequestSignChallenge" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.RequestSignChallenge"/>
    </bindings>
    <bindings scd="tns:SignTAChallengeResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SignTAChallengeResponse"/>
    </bindings>
    <bindings scd="~tns:ResponseSignChallenge" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.ResponseSignChallenge"/>
    </bindings>
    <bindings scd="tns:SendSDIRequestNew" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendSDIRequestNew"/>
    </bindings>
    <bindings scd="tns:SendSDIRequestNewResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendSDIRequestNewResponse"/>
    </bindings>
    <bindings scd="tns:SendSDIRequestS040" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendSDIRequestS040"/>
    </bindings>
    <bindings scd="tns:SendSDIRequestS040Response" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SendSDIRequestS040Response"/>
    </bindings>
    <bindings scd="tns:SIFServizioS000" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SIFServizioS000"/>
    </bindings>
    <bindings scd="~tns:IAERequestS000" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.IAERequestS000"/>
    </bindings>
    <bindings scd="tns:SIFServizioS000Response" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SIFServizioS000Response"/>
    </bindings>
    <bindings scd="tns:SifHeader" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SifHeader"/>
    </bindings>
    <bindings scd="tns:SIFServizioS004" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SIFServizioS004"/>
    </bindings>
    <bindings scd="~tns:IAERequestS004" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.IAERequestS004"/>
    </bindings>
    <bindings scd="tns:SIFServizioS004Response" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SIFServizioS004Response"/>
    </bindings>
    <bindings scd="tns:SIFServizioS016" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SIFServizioS016"/>
    </bindings>
    <bindings scd="~tns:IAERequestS016" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.IAERequestS016"/>
    </bindings>
    <bindings scd="tns:SIFServizioS016Response" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SIFServizioS016Response"/>
    </bindings>
    <bindings scd="~tns:Header" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.Header"/>
    </bindings>
    <bindings scd="~tns:ArrayOfIAEResponseMessagge" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.ArrayOfIAEResponseMessagge"/>
    </bindings>
    <bindings scd="~tns:IAEResponseMessagge" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.IAEResponseMessagge"/>
    </bindings>
    <bindings scd="~tns:ArrayOfString" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.ArrayOfString"/>
    </bindings>
    <bindings scd="~tns:ArrayOfDateTime" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.ArrayOfDateTime"/>
    </bindings>
    <bindings scd="~tns:ArrayOfInt" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.ArrayOfInt"/>
    </bindings>
    <bindings scd="~tns:ResultClass" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.ResultClass"/>
    </bindings>
    <bindings scd="~tns:CheckClass" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.CheckClass"/>
    </bindings>
    <bindings scd="~tns:PersonalDataClass" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.PersonalDataClass"/>
    </bindings>
    <bindings scd="~tns:NominativeClass" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.NominativeClass"/>
    </bindings>
    <bindings scd="~tns:TransliterateTextClass" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.TransliterateTextClass"/>
    </bindings>
    <bindings scd="~tns:TravelDocumentClass" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.TravelDocumentClass"/>
    </bindings>
    <bindings scd="~tns:VisaClass" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.VisaClass"/>
    </bindings>
    <bindings scd="~tns:IssuePlaceClass" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.IssuePlaceClass"/>
    </bindings>
    <bindings scd="~tns:StatusClass" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.StatusClass"/>
    </bindings>
    <bindings scd="~tns:ArrayOfIVISDecisioni" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.ArrayOfIVISDecisioni"/>
    </bindings>
    <bindings scd="~tns:IVISDecisioni" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.IVISDecisioni"/>
    </bindings>
    <bindings scd="~tns:AutoritaType" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.AutoritaType"/>
    </bindings>
    <bindings scd="~tns:LuogoType" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.LuogoType"/>
    </bindings>
    <bindings scd="~tns:TestoTraslitteratoType" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.TestoTraslitteratoType"/>
    </bindings>
    <bindings scd="~tns:PeriodoType" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.PeriodoType"/>
    </bindings>
    <bindings scd="~tns:ValiditaTerritorialeType" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.ValiditaTerritorialeType"/>
    </bindings>
    <bindings scd="~tns:VisaStickerType" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.VisaStickerType"/>
    </bindings>
    <bindings scd="~tns:ArrayOfIVISDecisioni1" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.ArrayOfIVISDecisioni1"/>
    </bindings>
    <bindings scd="~tns:HeaderClass" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.HeaderClass"/>
    </bindings>
    <bindings scd="~tns:BiometryClass" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.BiometryClass"/>
    </bindings>
    <bindings scd="~tns:SIFResponseTravellerAlert" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SIFResponseTravellerAlert"/>
    </bindings>
    <bindings scd="~tns:TipoVistoType" if-exists="true" auto-acknowledge="true">
      <typesafeEnumClass ref="com.reco.ees.gateway.generated.sif.iae.TipoVistoType"/>
    </bindings>
    <bindings scd="~tns:IngressiType" if-exists="true" auto-acknowledge="true">
      <typesafeEnumClass ref="com.reco.ees.gateway.generated.sif.iae.IngressiType"/>
    </bindings>
    <bindings scd="~tns:StatusVistoType" if-exists="true" auto-acknowledge="true">
      <typesafeEnumClass ref="com.reco.ees.gateway.generated.sif.iae.StatusVistoType"/>
    </bindings>
    <bindings scd="~tns:tipoVisto" if-exists="true" auto-acknowledge="true">
      <typesafeEnumClass ref="com.reco.ees.gateway.generated.sif.iae.TipoVisto"/>
    </bindings>
  </bindings>
  <bindings scd="x-schema::tns" if-exists="true" auto-acknowledge="true" xmlns:tns="http://schemas.sif.bcs.sita.it/">
    <schemaBindings map="false">
      <package name="com.reco.ees.gateway.generated.sif.iae"/>
    </schemaBindings>
    <bindings scd="~tns:BCSAlertResponse" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.BCSAlertResponse"/>
    </bindings>
    <bindings scd="~tns:Traveller" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.Traveller"/>
    </bindings>
    <bindings scd="~tns:positivitaTipo" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.PositivitaTipo"/>
    </bindings>
  </bindings>
  <bindings scd="x-schema::tns" if-exists="true" auto-acknowledge="true" xmlns:tns="http://www.interno.it/schengen/vis/webservice/common">
    <schemaBindings map="false">
      <package name="com.reco.ees.gateway.generated.sif.iae"/>
    </schemaBindings>
    <bindings scd="~tns:HeaderType" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.HeaderType"/>
    </bindings>
    <bindings scd="~tns:IVISAnagrafica" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.IVISAnagrafica"/>
    </bindings>
    <bindings scd="~tns:IVISDocumentoViaggio" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.IVISDocumentoViaggio"/>
    </bindings>
    <bindings scd="~tns:NominativoType" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.NominativoType"/>
    </bindings>
    <bindings scd="~tns:IVISTestoTraslitterato" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.IVISTestoTraslitterato"/>
    </bindings>
    <bindings scd="~tns:ArrayOfString" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.ArrayOfString2"/>
    </bindings>
    <bindings scd="~tns:ImprontaDigitaleType" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.ImprontaDigitaleType"/>
    </bindings>
    <bindings scd="~tns:SessoType" if-exists="true" auto-acknowledge="true">
      <typesafeEnumClass ref="com.reco.ees.gateway.generated.sif.iae.SessoType"/>
    </bindings>
  </bindings>
  <bindings scd="x-schema::tns" if-exists="true" auto-acknowledge="true" xmlns:tns="http://gestione.iae.sdi.ibm.it">
    <schemaBindings map="false">
      <package name="com.reco.ees.gateway.generated.sif.iae"/>
    </schemaBindings>
    <bindings scd="~tns:RispostaS000" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.RispostaS000"/>
    </bindings>
    <bindings scd="~tns:RichiestaS000" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.RichiestaS000"/>
    </bindings>
  </bindings>
  <bindings scd="x-schema::tns" if-exists="true" auto-acknowledge="true" xmlns:tns="http://interrogazione.iae.sdi.ibm.it">
    <schemaBindings map="false">
      <package name="com.reco.ees.gateway.generated.sif.iae"/>
    </schemaBindings>
    <bindings scd="~tns:RispostaS004" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.RispostaS004"/>
    </bindings>
    <bindings scd="~tns:RispostaS016" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.RispostaS016"/>
    </bindings>
    <bindings scd="~tns:S004" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.S004"/>
    </bindings>
    <bindings scd="~tns:InformativaReato" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.InformativaReato"/>
    </bindings>
    <bindings scd="~tns:Reato" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.Reato"/>
    </bindings>
    <bindings scd="~tns:PersoneDaTutelare" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.PersoneDaTutelare"/>
    </bindings>
    <bindings scd="~tns:PermessoSoggiorno" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.PermessoSoggiorno"/>
    </bindings>
    <bindings scd="~tns:AnagraficaCollegata" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.AnagraficaCollegata"/>
    </bindings>
    <bindings scd="~tns:Scomparso" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.Scomparso"/>
    </bindings>
    <bindings scd="~tns:S016" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.S016"/>
    </bindings>
    <bindings scd="~tns:Fatto" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.Fatto"/>
    </bindings>
    <bindings scd="~tns:PersonaFisica" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.PersonaFisica"/>
    </bindings>
    <bindings scd="~tns:PersonaGiuridica" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.PersonaGiuridica"/>
    </bindings>
    <bindings scd="~tns:Informativa" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.Informativa"/>
    </bindings>
    <bindings scd="~tns:S016SIS" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.S016SIS"/>
    </bindings>
    <bindings scd="~tns:RichiestaS004" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.RichiestaS004"/>
    </bindings>
    <bindings scd="~tns:RichiestaS016" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.RichiestaS016"/>
    </bindings>
  </bindings>
  <bindings scd="x-schema::tns" if-exists="true" auto-acknowledge="true" xmlns:tns="http://common.business.sdi.ibm.it">
    <schemaBindings map="false">
      <package name="com.reco.ees.gateway.generated.sif.iae"/>
    </schemaBindings>
    <bindings scd="~tns:WSRichiesta" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.WSRichiesta"/>
    </bindings>
    <bindings scd="~tns:WSRichiestaS000" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.WSRichiestaS000"/>
    </bindings>
    <bindings scd="~tns:WSRisposta" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.WSRisposta"/>
    </bindings>
    <bindings scd="~tns:WSMessaggio" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.WSMessaggio"/>
    </bindings>
    <bindings scd="~tns:SifHeaderType" if-exists="true" auto-acknowledge="true">
      <class ref="com.reco.ees.gateway.generated.sif.iae.SifHeaderType"/>
    </bindings>
  </bindings>
</bindings>

