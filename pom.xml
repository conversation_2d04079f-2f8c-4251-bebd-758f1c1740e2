<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>3.5.0</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.reco.ees</groupId>
	<artifactId>gateway</artifactId>
	<version>1.0.2</version>
	<name>gateway</name>
	<description>EES Gateway</description>
	<packaging>jar</packaging>

	<properties>
		<java.version>21</java.version> <!--1.8 o 17-->
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-devtools</artifactId>
			<scope>runtime</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-prometheus</artifactId>
			<version>1.15.1</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<scope>runtime</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!-- ================= SOAP ==================== -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web-services</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.ws</groupId>
			<artifactId>spring-ws-support</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-spring-boot-starter-jaxws</artifactId>
			<version>4.1.2</version>
		</dependency>

		<dependency>
			<groupId>org.apache.cxf</groupId>
			<artifactId>cxf-codegen-plugin</artifactId>
			<version>4.1.2</version>
		</dependency>

		<!-- ================= JSON ==================== -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.dataformat</groupId>
			<artifactId>jackson-dataformat-xml</artifactId>
		</dependency>

		<!-- ============= LOMBOK ================= -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.38</version>
			<scope>provided</scope>
		</dependency>

		<!--<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
			<version>2.19.0</version>
		</dependency>-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-log4j2</artifactId>
		</dependency>
		<!--<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-spring-boot</artifactId>
		</dependency>-->

		<!-- https://mvnrepository.com/artifact/jakarta.xml.bind/jakarta.xml.bind-api -->
		<dependency>
			<groupId>jakarta.xml.bind</groupId>
			<artifactId>jakarta.xml.bind-api</artifactId>
			<version>4.0.2</version>
		</dependency>
		<dependency>
			<groupId>org.glassfish.jaxb</groupId>
			<artifactId>jaxb-runtime</artifactId>
			<version>4.0.5</version>
		</dependency>

		<!-- COMMENTATO ANCHE METODI CHE USANO reflections COME SoapUtils.findAllClassesInPackages() e SoapUtils.mapSoapHeaderToObject() -->
		<!--<dependency>
			<groupId>org.reflections</groupId>
			<artifactId>reflections</artifactId>
			<version>0.10.2</version>
		</dependency>-->

		<!--<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-registry-graphite</artifactId>
		</dependency>--> <!--docker run -d &#45;&#45;name graphite &#45;&#45;restart=always -p 80:80 -p 2003-2004:2003-2004 -p 2023-2024:2023-2024 -p 8125:8125/udp -p 8126:8126 graphiteapp/graphite-statsd-->
		<!--<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>-->

		<!-- ============= ldaptive LDIF ================= -->
			<!--<dependency>
				<groupId>org.ldaptive</groupId>
				<artifactId>ldaptive-json</artifactId>
				<version>2.1.1</version>
			</dependency>-->

		<!--<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.13</version>
		</dependency>-->

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.13.1</version>
		</dependency>

		<!-- ============= EMAIL ================= -->

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-mail</artifactId>
		</dependency>

		<!-- ============= CACHING ================= -->
		<dependency>
			<groupId>org.hibernate.orm</groupId>
			<artifactId>hibernate-jcache</artifactId>
		</dependency>
		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>caffeine</artifactId>
			<version>3.2.1</version>
		</dependency>
		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>jcache</artifactId>
			<version>3.2.1</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<!--<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<version>3.3.1</version>
				<configuration>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>-->
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>versions-maven-plugin</artifactId>
				<version>2.18.0</version>
			</plugin>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.5.3</version> <!--<version>2.22.2</version>-->
				<configuration>
					<!--<skipTests>true</skipTests>-->
					<skipTests>false</skipTests>
				</configuration>
			</plugin>
			<plugin>
				<!--<groupId>org.jvnet.jaxb2.maven2</groupId>
				<artifactId>maven-jaxb2-plugin</artifactId>
				<version>0.15.3</version>-->
				<groupId>org.jvnet.jaxb</groupId>
				<artifactId>jaxb-maven-plugin</artifactId>
				<version>4.0.9</version> <!--<version>4.0.6</version>-->
				<!-- https://mvnrepository.com/artifact/org.jvnet.jaxb2.maven2/maven-jaxb23-plugin -->
				<!--<groupId>org.jvnet.jaxb2.maven2</groupId>
				<artifactId>maven-jaxb23-plugin</artifactId>
				<version>0.15.3</version>-->
				<!--<groupId>org.codehaus.mojo</groupId>
				<artifactId>jaxb2-maven-plugin</artifactId>
				<version>3.1.0</version>-->
				<!--<include>http://www.thomas-bayer.com/axis2/services/BLZService?wsdl</include>-->
				<executions>
					<execution>
						<id>FromFiles1</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<args>
								<arg>-wsdl</arg>
								<arg>-XautoNameResolution</arg>
							</args>
							<!--<verbose>true</verbose>
							<debug>true</debug>-->
							<cleanPackageDirectories>true</cleanPackageDirectories>
							<accessExternalSchema>all</accessExternalSchema>
							<generatePackage>com.reco.ees.gateway.generated.npkd.certificates</generatePackage>
							<generateDirectory>${project.basedir}/src/main/java</generateDirectory>
							<schemaDirectory>${project.basedir}/src/main/resources/soapFiles/NPKDCertificates</schemaDirectory>
							<schemaIncludes>
								<include>wsSIFNPKDProxy.wsdl</include>
							</schemaIncludes>
						</configuration>
					</execution>
					<execution>
						<id>FromFiles2</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<args>
								<arg>-wsdl</arg>
								<arg>-XautoNameResolution</arg>
							</args>
							<!--<verbose>true</verbose>
							<debug>true</debug>-->
							<cleanPackageDirectories>true</cleanPackageDirectories>
							<accessExternalSchema>all</accessExternalSchema>
							<generatePackage>com.reco.ees.gateway.generated.sif.response</generatePackage>
							<generateDirectory>${project.basedir}/src/main/java</generateDirectory>
							<schemaDirectory>${project.basedir}/src/main/resources/soapFiles/SIFResponseServices</schemaDirectory>
							<schemaIncludes>
								<!--<include>**/*.xsd</include>--> <!-- commentato perchè i data types definiti nel xsd hanno riferimenti in wsdl-->
								<include>**/*.wsdl</include>
							</schemaIncludes>
						</configuration>
					</execution>
					<execution>
						<id>FromFiles3</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<args>
								<arg>-wsdl</arg>
								<arg>-XautoNameResolution</arg>
							</args>
							<!--<verbose>true</verbose>
							<debug>true</debug>-->
							<cleanPackageDirectories>true</cleanPackageDirectories>
							<accessExternalSchema>all</accessExternalSchema>
							<!--<generatePackage>com.reco.ees.gateway.sifresponse</generatePackage>-->
							<!--<packageName>com.reco.ees.gateway.sifresponse</packageName>-->
							<generateDirectory>${project.basedir}/src/main/java</generateDirectory>
							<schemaDirectory>${project.basedir}/src/main/resources/soapFiles/EES</schemaDirectory>
							<schemaIncludes>
								<!--<include>**/*.xsd</include>-->
								<include>**/Services.wsdl</include> <!--qui i riferimenti ai data types degli xsd non sono inclusi nel wsdl e vorrei quindi includere anche gli xsd ma ho errore-->
							</schemaIncludes>
							<!--<namespace>http://www.europa.eu/schengen/ees/webservice/v1</namespace>-->
						</configuration>
					</execution>
					<execution>
						<id>FromFiles4</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<args>
								<arg>-wsdl</arg>
								<arg>-XautoNameResolution</arg>
							</args>
							<!--<verbose>true</verbose>
							<debug>true</debug>-->
							<cleanPackageDirectories>true</cleanPackageDirectories>
							<accessExternalSchema>all</accessExternalSchema>
							<!--<generatePackage>com.reco.ees.gateway.sifresponse</generatePackage>-->
							<!--<packageName>com.reco.ees.gateway.sifresponse</packageName>-->
							<generateDirectory>${project.basedir}/src/main/java</generateDirectory>
							<schemaDirectory>${project.basedir}/src/main/resources/soapFiles/EES</schemaDirectory>
							<schemaIncludes>
								<!--<include>**/*.xsd</include>-->
								<include>**/CallbackServices.wsdl</include> <!--qui i riferimenti ai data types degli xsd non sono inclusi nel wsdl e vorrei quindi includere anche gli xsd ma ho errore-->
							</schemaIncludes>
							<!--<namespace>http://www.europa.eu/schengen/ees/webservice/v1</namespace>-->
						</configuration>
					</execution>
					<execution>
						<id>FromFiles5</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<args>
								<arg>-wsdl</arg>
								<arg>-XautoNameResolution</arg>
							</args>
							<!--<verbose>true</verbose>
							<debug>true</debug>-->
							<cleanPackageDirectories>true</cleanPackageDirectories>
							<accessExternalSchema>all</accessExternalSchema>
							<generatePackage>com.reco.ees.gateway.generated.sif.services</generatePackage>
							<generateDirectory>${project.basedir}/src/main/java</generateDirectory>
							<schemaDirectory>${project.basedir}/src/main/resources/soapFiles/SIFServices</schemaDirectory>
							<schemaIncludes>
								<include>wsSIFServices.wsdl</include>
							</schemaIncludes>
						</configuration>
					</execution>
					<execution>
						<id>FromFiles6</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<args>
								<arg>-wsdl</arg>
								<arg>-XautoNameResolution</arg>
							</args>
							<!--<verbose>true</verbose>
							<debug>true</debug>-->
							<cleanPackageDirectories>true</cleanPackageDirectories>
							<accessExternalSchema>all</accessExternalSchema>
							<generatePackage>com.reco.ees.gateway.generated.sif.status</generatePackage>
							<generateDirectory>${project.basedir}/src/main/java</generateDirectory>
							<schemaDirectory>${project.basedir}/src/main/resources/soapFiles/SIFStatusService</schemaDirectory>
							<schemaIncludes>
								<!--<include>**/*.xsd</include>--> <!-- commentato perchè i data types definiti nel xsd hanno riferimenti in wsdl-->
								<include>**/*.wsdl</include>
							</schemaIncludes>
						</configuration>
					</execution>
					<execution>
						<id>FromFiles7</id>
						<goals>
							<goal>generate</goal>
						</goals>
						<configuration>
							<args>
								<arg>-wsdl</arg>
								<arg>-XautoNameResolution</arg>
							</args>
							<!--<verbose>true</verbose>
							<debug>true</debug>-->
							<cleanPackageDirectories>true</cleanPackageDirectories>
							<accessExternalSchema>all</accessExternalSchema>
							<generatePackage>com.reco.ees.gateway.generated.sif.iae</generatePackage> <!--in realta' ci sono anche servizi diversi da IAE-->
							<generateDirectory>${project.basedir}/src/main/java</generateDirectory>
							<schemaDirectory>${project.basedir}/src/main/resources/soapFiles/SIF_IAE_Services</schemaDirectory>
							<schemaIncludes>
								<!--<include>**/*.xsd</include>--> <!-- commentato perchè i data types definiti nel xsd hanno riferimenti in wsdl-->
								<include>**/*.wsdl</include> <!--wsSIFProxy.wsdl contiene attributo di versione JAXB pari a 1.0 e questa viene automaticamente aggiornata a 3.0-->
							</schemaIncludes>
						</configuration>
					</execution>
				</executions>
			</plugin>
<!--
			<plugin><groupId>org.apache.maven.plugins</groupId><artifactId>maven-compiler-plugin</artifactId><configuration><source>8</source><target>8</target></configuration></plugin>
-->
		</plugins>
	</build>

</project>
